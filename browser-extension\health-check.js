// Browser MCP Health Check Tool

class HealthChecker {
    constructor() {
        this.aiSites = {
            'chatgpt': {
                url: 'https://chat.openai.com',
                selectors: {
                    input: ['#prompt-textarea', 'textarea[data-id="root"]'],
                    send: ['button[data-testid="send-button"]'],
                    login: ['.auth-form', '[data-testid="login-button"]']
                }
            },
            'claude': {
                url: 'https://claude.ai',
                selectors: {
                    input: ['div[contenteditable="true"]', '.ProseMirror'],
                    send: ['button[aria-label="Send Message"]'],
                    login: ['.login-form', '.auth-container']
                }
            },
            'gemini': {
                url: 'https://gemini.google.com',
                selectors: {
                    input: ['rich-textarea[aria-label*="Enter a prompt"]', '.ql-editor'],
                    send: ['button[aria-label="Send message"]'],
                    login: ['.signin-container', '.login-button']
                }
            },
            'grok': {
                url: 'https://grok.x.ai',
                selectors: {
                    input: ['textarea[placeholder*="Ask Grok"]'],
                    send: ['button[aria-label="Send"]'],
                    login: ['.login-form', '.auth-form']
                }
            }
        };
    }
    
    async checkAllSites() {
        const results = {};
        
        for (const [model, config] of Object.entries(this.aiSites)) {
            results[model] = await this.checkSite(model, config);
        }
        
        return results;
    }
    
    async checkSite(model, config) {
        try {
            // 檢查分頁是否存在
            const tabs = await chrome.tabs.query({ url: config.url + '*' });
            
            if (tabs.length === 0) {
                return {
                    status: 'no_tab',
                    message: '分頁未開啟',
                    suggestions: [`請開啟 ${config.url}`]
                };
            }
            
            const tab = tabs[0];
            
            // 檢查分頁狀態
            if (tab.status !== 'complete') {
                return {
                    status: 'loading',
                    message: '分頁載入中',
                    suggestions: ['請等待分頁完全載入']
                };
            }
            
            // 注入檢查腳本
            const result = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                func: this.checkPageElements,
                args: [config.selectors]
            });
            
            const pageCheck = result[0].result;
            
            if (pageCheck.needsLogin) {
                return {
                    status: 'needs_login',
                    message: '需要登入',
                    suggestions: [`請登入 ${model.toUpperCase()}`]
                };
            }
            
            if (!pageCheck.hasInput) {
                return {
                    status: 'no_input',
                    message: '找不到輸入框',
                    suggestions: ['頁面可能已更新，需要更新選擇器']
                };
            }
            
            if (!pageCheck.hasSend) {
                return {
                    status: 'no_send',
                    message: '找不到發送按鈕',
                    suggestions: ['頁面可能已更新，需要更新選擇器']
                };
            }
            
            return {
                status: 'ready',
                message: '準備就緒',
                suggestions: []
            };
            
        } catch (error) {
            return {
                status: 'error',
                message: error.message,
                suggestions: ['檢查擴充功能權限', '重新載入分頁']
            };
        }
    }
    
    // 這個函數會在目標分頁中執行
    checkPageElements(selectors) {
        const findElement = (selectorList) => {
            for (const selector of selectorList) {
                const element = document.querySelector(selector);
                if (element) return element;
            }
            return null;
        };
        
        const hasInput = findElement(selectors.input) !== null;
        const hasSend = findElement(selectors.send) !== null;
        const needsLogin = findElement(selectors.login) !== null;
        
        return {
            hasInput,
            hasSend,
            needsLogin,
            url: window.location.href,
            title: document.title
        };
    }
    
    generateReport(results) {
        let report = '# Browser MCP 系統健康檢查報告\n\n';
        report += `檢查時間: ${new Date().toLocaleString()}\n\n`;
        
        let allReady = true;
        
        for (const [model, result] of Object.entries(results)) {
            const statusIcon = {
                'ready': '✅',
                'no_tab': '❌',
                'loading': '⏳',
                'needs_login': '🔐',
                'no_input': '⚠️',
                'no_send': '⚠️',
                'error': '💥'
            }[result.status] || '❓';
            
            report += `## ${statusIcon} ${model.toUpperCase()}\n`;
            report += `**狀態**: ${result.message}\n`;
            
            if (result.suggestions.length > 0) {
                report += `**建議**:\n`;
                result.suggestions.forEach(suggestion => {
                    report += `- ${suggestion}\n`;
                });
                allReady = false;
            }
            
            report += '\n';
        }
        
        report += '## 總結\n';
        if (allReady) {
            report += '🎉 所有AI網站都已準備就緒！可以開始使用Browser MCP。\n';
        } else {
            report += '⚠️ 部分AI網站需要處理，請按照上述建議操作。\n';
        }
        
        return report;
    }
}

// 導出給background script使用
if (typeof chrome !== 'undefined' && chrome.runtime) {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.type === 'HEALTH_CHECK') {
            const checker = new HealthChecker();
            checker.checkAllSites().then(results => {
                sendResponse({
                    results: results,
                    report: checker.generateReport(results)
                });
            });
            return true; // 保持訊息通道開啟
        }
    });
}
