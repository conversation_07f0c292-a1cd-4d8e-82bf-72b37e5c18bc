# Browser MCP Multi-LLM 部署指南

## 🎯 系統概述

Browser MCP Multi-LLM 是一個真正的瀏覽器自動化系統，可以同時控制 ChatGPT、<PERSON>、Gemini、Grok 四個AI網站，實現一次輸入、多重回應的功能。

## 📁 專案結構

```
202506-LLM大亂鬥/
├── browser-extension/           # Chrome擴充功能
│   ├── manifest.json           # 擴充功能配置
│   ├── background.js           # 背景腳本（核心邏輯）
│   ├── popup.html/js           # 擴充功能彈出視窗
│   ├── content-scripts/        # 內容腳本
│   │   ├── chatgpt.js         # ChatGPT控制
│   │   ├── claude.js          # Claude控制
│   │   ├── gemini.js          # Gemini控制
│   │   └── grok.js            # Grok控制
│   └── icons/                 # 擴充功能圖標
├── frontend/                   # 控制面板
│   ├── forum.html             # 主介面
│   ├── app.js                 # 前端邏輯
│   └── styles.css             # 樣式文件
├── start.bat                  # 快速啟動
├── test-system.bat            # 系統測試
└── 安裝指南.md                # 使用說明
```

## 🚀 快速部署

### 方法一：自動測試部署

1. **執行測試腳本**
   ```bash
   雙擊 test-system.bat
   ```

2. **按照提示操作**
   - 載入Chrome擴充功能
   - 登入所有AI網站
   - 測試控制面板

### 方法二：手動部署

#### 步驟1：安裝Chrome擴充功能

1. 開啟Chrome瀏覽器
2. 前往 `chrome://extensions/`
3. 開啟「開發人員模式」
4. 點擊「載入未封裝項目」
5. 選擇 `browser-extension` 資料夾

#### 步驟2：登入AI網站

**必須登入以下網站：**
- ChatGPT: https://chat.openai.com
- Claude: https://claude.ai
- Gemini: https://gemini.google.com
- Grok: https://grok.x.ai

#### 步驟3：啟動控制面板

```bash
雙擊 start.bat
# 或直接開啟 frontend/forum.html
```

## 🔧 技術架構

### Chrome擴充功能架構

```javascript
// Manifest V3 架構
{
  "manifest_version": 3,
  "permissions": ["activeTab", "tabs", "storage", "scripting"],
  "host_permissions": [
    "https://chat.openai.com/*",
    "https://claude.ai/*", 
    "https://gemini.google.com/*",
    "https://grok.x.ai/*"
  ]
}
```

### 通訊機制

1. **forum.html ↔ background.js**
   ```javascript
   // 使用chrome.runtime.connect建立持久連接
   const port = chrome.runtime.connect({ name: 'forum-connection' });
   ```

2. **background.js ↔ content scripts**
   ```javascript
   // 使用chrome.tabs.sendMessage發送指令
   chrome.tabs.sendMessage(tabId, { type: 'SEND_PROMPT', prompt });
   ```

3. **content scripts → background.js**
   ```javascript
   // 使用chrome.runtime.sendMessage回報狀態
   chrome.runtime.sendMessage({ type: 'RESPONSE_CHUNK', chunk });
   ```

### DOM操作策略

每個AI網站都有專門的content script：

```javascript
// 以ChatGPT為例
class ChatGPTController {
    findInputBox() {
        const selectors = [
            '#prompt-textarea',
            'textarea[data-id="root"]',
            'form textarea'
        ];
        // 多重選擇器確保相容性
    }
    
    setupResponseListener() {
        // 使用MutationObserver監聽回應
        this.responseObserver = new MutationObserver(mutations => {
            this.handleResponseMutation(mutations);
        });
    }
}
```

## 🛡️ 安全與限制

### 權限控制

- **最小權限原則**：只請求必要的網站權限
- **無<all_urls>**：避免過度權限請求
- **本地處理**：所有資料在本地瀏覽器處理

### 反機器人策略

- **速率限制**：每秒最多1個請求
- **自然操作**：模擬真實用戶行為
- **錯誤處理**：優雅處理網站變更

### 相容性考量

- **多重選擇器**：應對網站UI更新
- **錯誤恢復**：自動重試機制
- **狀態監控**：即時反饋系統狀態

## 📊 監控與除錯

### 狀態監控

1. **擴充功能狀態**
   - 點擊工具列圖標查看popup
   - 檢查各AI網站連接狀態

2. **控制面板狀態**
   - 右上角狀態指示器
   - 即時顯示連接狀態

### 除錯工具

1. **Chrome開發者工具**
   ```javascript
   // 在Console中檢查
   console.log('Extension status:', chrome.runtime);
   ```

2. **擴充功能除錯**
   - 前往 `chrome://extensions/`
   - 點擊「檢查檢視」→「背景頁面」

3. **Content Script除錯**
   - 在AI網站按F12
   - 查看Console錯誤訊息

## 🔄 更新與維護

### 網站適配更新

當AI網站更新UI時，需要更新對應的content script：

```javascript
// 更新DOM選擇器
const selectors = [
    '#new-input-selector',    // 新增選擇器
    '#prompt-textarea',       // 保留舊選擇器
    'textarea[data-id="root"]'
];
```

### 功能擴展

1. **新增AI網站**
   - 創建新的content script
   - 更新manifest.json權限
   - 修改background.js邏輯

2. **增強功能**
   - 添加回應格式化
   - 實現對話歷史
   - 支援檔案上傳

## 🚨 故障排除

### 常見問題

1. **擴充功能無法載入**
   ```
   解決：檢查manifest.json語法，確認權限設定
   ```

2. **AI網站無回應**
   ```
   解決：確認已登入，檢查DOM選擇器是否正確
   ```

3. **回應顯示異常**
   ```
   解決：重新載入擴充功能，清除瀏覽器快取
   ```

### 除錯步驟

1. **檢查擴充功能狀態**
2. **確認AI網站登入狀態**
3. **查看Console錯誤訊息**
4. **測試單一AI網站**
5. **重新載入擴充功能**

## 📈 效能優化

### 記憶體管理

- **及時清理Observer**：避免記憶體洩漏
- **限制並發請求**：防止瀏覽器過載
- **快取DOM元素**：減少重複查詢

### 網路優化

- **批次處理**：合併多個操作
- **錯誤重試**：智能重試機制
- **超時處理**：避免無限等待

## 🎯 最佳實踐

1. **使用前測試**：確保所有AI網站正常登入
2. **適度使用**：避免頻繁請求觸發限制
3. **定期更新**：關注AI網站UI變更
4. **備份設定**：保存重要的對話記錄
5. **監控狀態**：注意系統狀態指示器

## 📝 版本資訊

- **當前版本**：v1.0.0
- **發布日期**：2024-12-29
- **相容性**：Chrome 88+ (Manifest V3)
- **支援平台**：Windows, macOS, Linux

---

**注意**：此系統為實驗性質，請遵守各AI平台的使用條款，合理使用避免觸發反機器人機制。
