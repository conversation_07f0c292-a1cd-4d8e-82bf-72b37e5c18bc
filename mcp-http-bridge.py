#!/usr/bin/env python3
"""
HTTP Bridge for Web Eval Agent MCP Server
將 HTTP 請求轉換為 MCP 調用
"""

import asyncio
import json
import os
import subprocess
import sys
import uuid
from typing import Dict, Any, List
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import time

class MCPBridge:
    """MCP 服務器橋接器"""

    def __init__(self):
        self.mcp_process = None
        # 嘗試導入簡化版 MCP 模組
        try:
            # 添加當前目錄到 Python 路徑
            current_dir = os.path.dirname(os.path.abspath(__file__))
            if current_dir not in sys.path:
                sys.path.insert(0, current_dir)

            from simple_multi_llm import handle_simple_multi_llm_prompt
            self.handle_multi_llm_prompt = handle_simple_multi_llm_prompt
            self.direct_call_available = True
            print("✅ 成功導入簡化版 MCP 模組，將使用直接調用")
        except Exception as e:
            print(f"⚠️  無法導入簡化版 MCP 模組，將使用子進程調用: {e}")
            self.direct_call_available = False

    async def call_mcp_tool(self, tool_name: str, params: Dict[str, Any]) -> str:
        """調用 MCP 工具"""
        if tool_name == 'send_prompt_to_llms' and self.direct_call_available:
            return await self.call_direct(params)
        else:
            return await self.call_subprocess(tool_name, params)

    async def call_direct(self, params: Dict[str, Any]) -> str:
        """直接調用簡化版 MCP 工具"""
        try:
            prompt = params.get('prompt', '')
            models = params.get('models', ['chatgpt', 'claude', 'gemini', 'grok'])

            print(f"🔧 直接調用: prompt='{prompt}', models={models}")
            result = await self.handle_multi_llm_prompt(prompt, models)
            print(f"✅ 直接調用成功，結果長度: {len(result)}")
            return result

        except Exception as e:
            print(f"❌ 直接調用失敗: {e}")
            # 不要回退到子進程，直接返回錯誤
            return f"❌ 直接調用失敗: {str(e)}"

    async def call_subprocess(self, tool_name: str, params: Dict[str, Any]) -> str:
        """使用子進程調用 MCP 工具"""
        try:
            # 構建 MCP 調用命令
            cmd = [
                sys.executable, "-m", "webEvalAgent.mcp_server",
                "--local"  # 使用本地模式避免 API key 需求
            ]

            # 創建 MCP 請求
            mcp_request = {
                "jsonrpc": "2.0",
                "id": str(uuid.uuid4()),
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": params
                }
            }

            # 調用 MCP 服務器
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd="web-eval-agent",
                env={**os.environ, "PYTHONIOENCODING": "utf-8", "PYTHONUTF8": "1"}  # 強制 UTF-8
            )

            # 發送請求
            request_json = json.dumps(mcp_request, ensure_ascii=False) + "\n"
            stdout, stderr = await process.communicate(request_json.encode('utf-8'))

            if process.returncode != 0:
                # 嘗試不同的編碼方式解碼錯誤信息
                try:
                    error_msg = stderr.decode('utf-8')
                except UnicodeDecodeError:
                    try:
                        error_msg = stderr.decode('gbk')
                    except UnicodeDecodeError:
                        error_msg = stderr.decode('utf-8', errors='ignore')
                return f"❌ MCP 調用失敗: {error_msg}"

            # 解析回應 - 嘗試不同編碼
            try:
                # Windows 系統優先嘗試 GBK，然後 UTF-8
                try:
                    stdout_text = stdout.decode('gbk')
                except UnicodeDecodeError:
                    try:
                        stdout_text = stdout.decode('utf-8')
                    except UnicodeDecodeError:
                        stdout_text = stdout.decode('utf-8', errors='replace')

                response = json.loads(stdout_text)
                if "result" in response:
                    # 提取文本內容
                    result = response["result"]
                    if isinstance(result, list) and len(result) > 0:
                        if isinstance(result[0], dict) and "text" in result[0]:
                            return result[0]["text"]
                    return str(result)
                elif "error" in response:
                    return f"❌ MCP 錯誤: {response['error']}"
                else:
                    return f"❌ 未知回應格式: {response}"
            except json.JSONDecodeError as e:
                return f"❌ 回應解析失敗: {e}\n原始輸出: {stdout_text}"

        except Exception as e:
            return f"❌ 調用異常: {str(e)}"

class HTTPHandler(BaseHTTPRequestHandler):
    """HTTP 請求處理器"""
    
    def __init__(self, *args, mcp_bridge=None, **kwargs):
        self.mcp_bridge = mcp_bridge
        super().__init__(*args, **kwargs)
    
    def do_OPTIONS(self):
        """處理 CORS 預檢請求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def do_POST(self):
        """處理 POST 請求"""
        try:
            # 解析 URL
            parsed_path = urlparse(self.path)
            
            # 讀取請求體
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            
            if parsed_path.path == '/send_prompt_to_llms':
                self.handle_send_prompt_to_llms(post_data)
            else:
                self.send_error(404, "Not Found")
                
        except Exception as e:
            self.send_error(500, f"Internal Server Error: {str(e)}")
    
    def handle_send_prompt_to_llms(self, post_data):
        """處理發送 prompt 到 LLMs 的請求"""
        try:
            # 解析 JSON 請求
            request_data = json.loads(post_data.decode())
            prompt = request_data.get('prompt', '')
            models = request_data.get('models', ['chatgpt', 'claude', 'gemini', 'grok'])
            
            if not prompt:
                self.send_json_response({"error": "No prompt provided"}, 400)
                return
            
            # 調用 MCP 工具
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                result = loop.run_until_complete(
                    self.mcp_bridge.call_mcp_tool('send_prompt_to_llms', {
                        'prompt': prompt,
                        'models': models
                    })
                )
                
                # 發送回應
                self.send_text_response(result)
                
            finally:
                loop.close()
                
        except json.JSONDecodeError:
            self.send_json_response({"error": "Invalid JSON"}, 400)
        except Exception as e:
            self.send_json_response({"error": str(e)}, 500)
    
    def send_json_response(self, data: Dict, status_code: int = 200):
        """發送 JSON 回應"""
        response_json = json.dumps(data, ensure_ascii=False)
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(response_json.encode('utf-8'))
    
    def send_text_response(self, text: str, status_code: int = 200):
        """發送文本回應"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'text/plain; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(text.encode('utf-8'))
    
    def log_message(self, format, *args):
        """自定義日誌格式"""
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def create_handler(mcp_bridge):
    """創建帶有 MCP 橋接器的處理器"""
    def handler(*args, **kwargs):
        return HTTPHandler(*args, mcp_bridge=mcp_bridge, **kwargs)
    return handler

def main():
    """主函數"""
    print("🚀 啟動 MCP HTTP 橋接服務器...")
    
    # 創建 MCP 橋接器
    mcp_bridge = MCPBridge()
    
    # 創建 HTTP 服務器
    server_address = ('localhost', 5000)
    handler_class = create_handler(mcp_bridge)
    httpd = HTTPServer(server_address, handler_class)
    
    print(f"✅ HTTP 橋接服務器啟動成功")
    print(f"📡 服務器地址: http://{server_address[0]}:{server_address[1]}")
    print(f"🔗 端點: /send_prompt_to_llms")
    print(f"📝 使用方法: POST JSON 請求包含 'prompt' 和 'models' 參數")
    print(f"⏹️  按 Ctrl+C 停止服務器")
    print()
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 正在停止服務器...")
        httpd.shutdown()
        print("✅ 服務器已停止")

if __name__ == "__main__":
    main()
