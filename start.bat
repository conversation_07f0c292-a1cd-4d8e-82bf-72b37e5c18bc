@echo off
cd /d "%~dp0"

echo.
echo ========================================
echo   Multi-LLM System Startup
echo ========================================
echo.

echo Checking system requirements...

:: Check Python and uv
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found, please install Python first
    pause
    exit /b 1
)

uv --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: uv not found, please install uv first
    echo Install command: curl -LsSf https://astral.sh/uv/install.sh ^| sh
    pause
    exit /b 1
)

echo OK: Python and uv are installed

echo.
echo Starting MCP HTTP Bridge Server...
echo Server address: http://localhost:5000
echo.

:: Start HTTP Bridge Server
start "MCP HTTP Bridge Server" cmd /k "python mcp-http-bridge.py"

:: Wait for server to start
echo Waiting for server to start...
timeout /t 5 /nobreak >nul

echo.
echo Opening frontend interface...
start "" "forum.html"

echo.
echo ========================================
echo   System Startup Complete!
echo ========================================
echo.
echo Usage Instructions:
echo 1. Frontend interface opened in browser
echo 2. MCP server running at http://localhost:5000
echo 3. Enter questions, select AI models, click send
echo 4. System will auto-control browsers to send requests
echo.
echo Troubleshooting:
echo - If frontend shows connection error, check MCP server
echo - Make sure you are logged into AI websites
echo - To restart server, close command window and rerun
echo.
echo Press any key to close this window...
pause >nul
