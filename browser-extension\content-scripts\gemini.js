// Gemini Content Script

class GeminiController {
    constructor() {
        this.model = 'gemini';
        this.isProcessing = false;
        this.responseObserver = null;
        this.lastResponseLength = 0;
        
        this.init();
    }
    
    init() {
        console.log('Gemini content script loaded');
        
        // 通知background script已準備就緒
        chrome.runtime.sendMessage({
            type: 'CONTENT_SCRIPT_READY',
            model: this.model
        });
        
        // 監聽來自background的訊息
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true;
        });
        
        // 等待頁面完全載入
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupPage());
        } else {
            this.setupPage();
        }
    }
    
    setupPage() {
        // 檢查是否已登入
        this.checkLoginStatus();
    }
    
    checkLoginStatus() {
        // Gemini登入檢查邏輯
        const loginIndicators = [
            'rich-textarea[aria-label*="Enter a prompt"]',
            'div[contenteditable="true"]',
            'button[aria-label="Send message"]',
            '.ql-editor'
        ];
        
        const isLoggedIn = loginIndicators.some(selector => 
            document.querySelector(selector) !== null
        );
        
        if (isLoggedIn) {
            console.log('Gemini: User is logged in');
        } else {
            console.log('Gemini: User needs to log in');
            this.sendError('請先登入Gemini');
        }
    }
    
    handleMessage(message, sender, sendResponse) {
        switch (message.type) {
            case 'SEND_PROMPT':
                this.sendPrompt(message.prompt);
                break;
        }
    }
    
    async sendPrompt(prompt) {
        if (this.isProcessing) {
            this.sendError('Gemini正在處理中，請稍候');
            return;
        }
        
        try {
            this.isProcessing = true;
            this.lastResponseLength = 0;
            
            // 尋找輸入框
            const inputBox = this.findInputBox();
            if (!inputBox) {
                throw new Error('找不到Gemini輸入框');
            }
            
            // 清空並輸入prompt
            await this.typeInInput(inputBox, prompt);
            
            // 設置回應監聽器
            this.setupResponseListener();
            
            // 點擊發送按鈕
            const sendButton = this.findSendButton();
            if (!sendButton) {
                throw new Error('找不到Gemini發送按鈕');
            }
            
            sendButton.click();
            
        } catch (error) {
            console.error('Gemini send prompt error:', error);
            this.sendError(error.message);
            this.isProcessing = false;
        }
    }
    
    findInputBox() {
        const selectors = [
            'rich-textarea[aria-label*="Enter a prompt"]',
            '.ql-editor',
            'div[contenteditable="true"]',
            'textarea[aria-label*="prompt"]'
        ];
        
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element && element.offsetParent !== null) { // 確保元素可見
                return element;
            }
        }
        
        return null;
    }
    
    findSendButton() {
        const selectors = [
            'button[aria-label="Send message"]',
            'button[data-testid="send-button"]',
            'button:has(svg[data-testid="send-icon"])',
            'button[type="submit"]'
        ];
        
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element && !element.disabled) return element;
        }
        
        return null;
    }
    
    async typeInInput(inputBox, text) {
        // 清空輸入框
        inputBox.textContent = '';
        inputBox.innerHTML = '';
        
        // 觸發focus事件
        inputBox.focus();
        
        // 模擬輸入
        if (inputBox.tagName === 'TEXTAREA') {
            inputBox.value = text;
            inputBox.dispatchEvent(new Event('input', { bubbles: true }));
        } else {
            // contenteditable div或rich-textarea
            inputBox.textContent = text;
            
            // 觸發必要的事件
            inputBox.dispatchEvent(new Event('input', { bubbles: true }));
            inputBox.dispatchEvent(new Event('keyup', { bubbles: true }));
            
            // 對於rich-textarea，可能需要特殊處理
            if (inputBox.tagName === 'RICH-TEXTAREA') {
                const event = new CustomEvent('input', { 
                    bubbles: true,
                    detail: { text: text }
                });
                inputBox.dispatchEvent(event);
            }
        }
        
        // 等待一下讓UI更新
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    setupResponseListener() {
        // 尋找回應容器
        const responseContainer = this.findResponseContainer();
        if (!responseContainer) {
            console.warn('Gemini: Could not find response container');
            return;
        }
        
        // 設置MutationObserver監聽回應
        this.responseObserver = new MutationObserver((mutations) => {
            this.handleResponseMutation(mutations);
        });
        
        this.responseObserver.observe(responseContainer, {
            childList: true,
            subtree: true,
            characterData: true
        });
    }
    
    findResponseContainer() {
        const selectors = [
            '[data-testid="conversation-turn-3"]',
            '.conversation-container',
            'main',
            '[role="main"]'
        ];
        
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) return element;
        }
        
        return document.body;
    }
    
    handleResponseMutation(mutations) {
        // 獲取最新的回應內容
        const responseText = this.extractLatestResponse();
        
        if (responseText && responseText.length > this.lastResponseLength) {
            const newChunk = responseText.substring(this.lastResponseLength);
            this.lastResponseLength = responseText.length;
            
            // 發送新的文字塊
            chrome.runtime.sendMessage({
                type: 'RESPONSE_CHUNK',
                model: this.model,
                chunk: newChunk
            });
        }
        
        // 檢查是否完成
        if (this.isResponseComplete()) {
            this.handleResponseComplete(responseText);
        }
    }
    
    extractLatestResponse() {
        // 嘗試多種選擇器來獲取最新回應
        const selectors = [
            '[data-testid="model-response-text"]',
            '.model-response-text',
            '[role="presentation"]:last-child',
            '.response-container:last-child'
        ];
        
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
                return element.textContent || element.innerText;
            }
        }
        
        return '';
    }
    
    isResponseComplete() {
        // 檢查是否有停止按鈕（表示正在生成）
        const stopButton = document.querySelector('button[aria-label="Stop generating"]');
        if (stopButton) return false;
        
        // 檢查發送按鈕是否可用（表示生成完成）
        const sendButton = this.findSendButton();
        return sendButton && !sendButton.disabled;
    }
    
    handleResponseComplete(fullResponse) {
        if (this.responseObserver) {
            this.responseObserver.disconnect();
            this.responseObserver = null;
        }
        
        this.isProcessing = false;
        
        chrome.runtime.sendMessage({
            type: 'RESPONSE_COMPLETE',
            model: this.model,
            response: fullResponse
        });
    }
    
    sendError(errorMessage) {
        chrome.runtime.sendMessage({
            type: 'ERROR',
            model: this.model,
            error: errorMessage
        });
    }
}

// 初始化Gemini控制器
const geminiController = new GeminiController();
