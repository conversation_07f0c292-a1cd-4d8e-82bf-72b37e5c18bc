@echo off
chcp 65001 >nul
title 一鍵啟動 - Browser MCP 多 LLM 系統

echo.
echo ========================================
echo   🚀 一鍵啟動 Browser MCP 多 LLM 系統
echo ========================================
echo.

echo 🔍 檢查並自動安裝必要工具...

REM 檢查 Chocolatey
choco --version >nul 2>&1
if errorlevel 1 (
    echo 📦 安裝 Chocolatey 包管理器...
    powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))"
    if errorlevel 1 (
        echo ❌ Chocolatey 安裝失敗，嘗試手動安裝...
        goto manual_install
    )
    echo ✅ Chocolatey 安裝完成
    refreshenv
)

REM 檢查並安裝 Python
python --version >nul 2>&1
if errorlevel 1 (
    echo 🐍 安裝 Python...
    choco install python -y
    refreshenv
)

REM 檢查並安裝 Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo 📗 安裝 Node.js...
    choco install nodejs -y
    refreshenv
)

REM 檢查並安裝 Git
git --version >nul 2>&1
if errorlevel 1 (
    echo 📚 安裝 Git...
    choco install git -y
    refreshenv
)

REM 安裝 uv
pip install uv >nul 2>&1
if errorlevel 1 (
    echo 📦 安裝 uv...
    python -m pip install uv
)

echo ✅ 所有工具安裝完成！

goto start_system

:manual_install
echo.
echo ❌ 自動安裝失敗，請手動安裝以下工具：
echo   1. Python: https://www.python.org/downloads/
echo   2. Node.js: https://nodejs.org/
echo   3. 安裝時請勾選 "Add to PATH"
echo.
echo 安裝完成後請重新運行此腳本
pause
exit /b 1

:start_system
echo.
echo 🚀 啟動系統...
echo.

REM 安裝 Python 依賴
echo 📦 安裝 Python 依賴...
cd web-eval-agent
uv pip install -e . >nul 2>&1
if errorlevel 1 (
    pip install -e . >nul 2>&1
)

REM 安裝 Playwright
echo 🎭 安裝 Playwright...
uv run playwright install chromium >nul 2>&1
if errorlevel 1 (
    python -m playwright install chromium >nul 2>&1
)

cd ..

REM 安裝 Node.js 依賴
echo 📗 安裝 Node.js 依賴...
cd prompt-proxy
npm install >nul 2>&1
cd ..

REM 啟動 MCP 服務器
echo 🔧 啟動 MCP 服務器...
cd web-eval-agent
start /B uv run python -m webEvalAgent.mcp_server --local --host 0.0.0.0 --port 5000
cd ..

REM 等待 MCP 服務器啟動
echo ⏳ 等待 MCP 服務器啟動...
timeout /t 5 /nobreak >nul

REM 啟動 Proxy 服務器
echo 🌐 啟動 Proxy 服務器...
cd prompt-proxy
start /B node server.js
cd ..

REM 等待 Proxy 服務器啟動
echo ⏳ 等待 Proxy 服務器啟動...
timeout /t 3 /nobreak >nul

REM 打開瀏覽器
echo 🌐 打開瀏覽器...
start http://localhost:3000

echo.
echo ========================================
echo   🎉 系統啟動完成！
echo ========================================
echo.
echo 📊 服務狀態:
echo   - MCP 服務器: http://localhost:5000
echo   - Proxy 服務器: http://localhost:3000
echo   - 前端介面: http://localhost:3000
echo.
echo 💡 使用說明:
echo   - 瀏覽器已自動打開
echo   - 選擇要使用的 LLM 模型
echo   - 輸入問題並發送到多個 AI
echo   - 關閉此視窗即可停止服務
echo.
echo 🎊 開始您的多 LLM 大亂鬥吧！
echo.

pause
