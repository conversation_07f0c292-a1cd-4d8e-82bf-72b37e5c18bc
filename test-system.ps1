# Browser MCP 多 LLM 系統測試腳本
# 使用方式: .\test-system.ps1

param(
    [int]$McpPort = 5000,
    [int]$ProxyPort = 3000,
    [switch]$Verbose,
    [switch]$Help
)

if ($Help) {
    Write-Host "Browser MCP 多 LLM 系統測試腳本" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "使用方式:" -ForegroundColor Yellow
    Write-Host "  .\test-system.ps1                # 基本測試"
    Write-Host "  .\test-system.ps1 -Verbose       # 詳細輸出"
    Write-Host "  .\test-system.ps1 -Help          # 顯示幫助"
    Write-Host ""
    Write-Host "參數說明:" -ForegroundColor Yellow
    Write-Host "  -McpPort     MCP 服務器埠號（預設: 5000）"
    Write-Host "  -ProxyPort   Proxy 服務器埠號（預設: 3000）"
    Write-Host "  -Verbose     顯示詳細測試資訊"
    Write-Host "  -Help        顯示此幫助訊息"
    exit 0
}

Write-Host "🧪 Browser MCP 多 LLM 系統測試" -ForegroundColor Cyan
Write-Host "=" * 50

$testResults = @()

function Test-Endpoint {
    param(
        [string]$Name,
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Body = $null,
        [int]$ExpectedStatus = 200,
        [int]$TimeoutSec = 10
    )
    
    Write-Host "`n🔍 測試: $Name" -ForegroundColor Yellow
    if ($Verbose) {
        Write-Host "   URL: $Url" -ForegroundColor Gray
        Write-Host "   Method: $Method" -ForegroundColor Gray
    }
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            TimeoutSec = $TimeoutSec
            ErrorAction = 'Stop'
        }
        
        if ($Body) {
            $params.Body = ($Body | ConvertTo-Json -Depth 10)
            $params.ContentType = 'application/json'
        }
        
        $response = Invoke-WebRequest @params
        
        if ($response.StatusCode -eq $ExpectedStatus) {
            Write-Host "✅ $Name - 成功 (狀態碼: $($response.StatusCode))" -ForegroundColor Green
            
            if ($Verbose -and $response.Content) {
                $content = $response.Content | ConvertFrom-Json -ErrorAction SilentlyContinue
                if ($content) {
                    Write-Host "   回應: $($content | ConvertTo-Json -Compress)" -ForegroundColor Gray
                }
            }
            
            return @{
                Name = $Name
                Status = "成功"
                StatusCode = $response.StatusCode
                ResponseTime = $null
                Error = $null
            }
        } else {
            Write-Host "❌ $Name - 失敗 (預期狀態碼: $ExpectedStatus, 實際: $($response.StatusCode))" -ForegroundColor Red
            return @{
                Name = $Name
                Status = "失敗"
                StatusCode = $response.StatusCode
                ResponseTime = $null
                Error = "狀態碼不符預期"
            }
        }
        
    } catch {
        Write-Host "❌ $Name - 錯誤: $($_.Exception.Message)" -ForegroundColor Red
        return @{
            Name = $Name
            Status = "錯誤"
            StatusCode = $null
            ResponseTime = $null
            Error = $_.Exception.Message
        }
    }
}

# 測試 1: MCP 服務器健康檢查
$testResults += Test-Endpoint -Name "MCP 服務器健康檢查" -Url "http://localhost:$McpPort/health"

# 測試 2: Proxy 服務器健康檢查
$testResults += Test-Endpoint -Name "Proxy 服務器健康檢查" -Url "http://localhost:$ProxyPort/health"

# 測試 3: Proxy 到 MCP 的連接
$testResults += Test-Endpoint -Name "Proxy 到 MCP 連接檢查" -Url "http://localhost:$ProxyPort/api/mcp-health"

# 測試 4: 前端頁面載入
$testResults += Test-Endpoint -Name "前端頁面載入" -Url "http://localhost:$ProxyPort/"

# 測試 5: 瀏覽器分頁管理
$testResults += Test-Endpoint -Name "瀏覽器分頁查詢" -Url "http://localhost:$ProxyPort/api/browser-tabs"

# 測試 6: 簡單 Prompt 發送測試
Write-Host "`n🔍 測試: Prompt 發送功能" -ForegroundColor Yellow
try {
    $testPrompt = "Hello, this is a test prompt for the multi-LLM system."
    $testBody = @{
        prompt = $testPrompt
        models = @("chatgpt")  # 只測試一個模型以減少測試時間
    }
    
    if ($Verbose) {
        Write-Host "   測試 Prompt: $testPrompt" -ForegroundColor Gray
    }
    
    $response = Invoke-WebRequest -Uri "http://localhost:$ProxyPort/api/send-prompt" -Method POST -Body ($testBody | ConvertTo-Json) -ContentType 'application/json' -TimeoutSec 30
    
    if ($response.StatusCode -eq 200) {
        $responseData = $response.Content | ConvertFrom-Json
        if ($responseData.success) {
            Write-Host "✅ Prompt 發送功能 - 成功" -ForegroundColor Green
            $testResults += @{
                Name = "Prompt 發送功能"
                Status = "成功"
                StatusCode = 200
                ResponseTime = $null
                Error = $null
            }
        } else {
            Write-Host "❌ Prompt 發送功能 - 失敗: $($responseData.error)" -ForegroundColor Red
            $testResults += @{
                Name = "Prompt 發送功能"
                Status = "失敗"
                StatusCode = 200
                ResponseTime = $null
                Error = $responseData.error
            }
        }
    } else {
        Write-Host "❌ Prompt 發送功能 - HTTP 錯誤 (狀態碼: $($response.StatusCode))" -ForegroundColor Red
        $testResults += @{
            Name = "Prompt 發送功能"
            Status = "失敗"
            StatusCode = $response.StatusCode
            ResponseTime = $null
            Error = "HTTP 錯誤"
        }
    }
    
} catch {
    Write-Host "❌ Prompt 發送功能 - 錯誤: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += @{
        Name = "Prompt 發送功能"
        Status = "錯誤"
        StatusCode = $null
        ResponseTime = $null
        Error = $_.Exception.Message
    }
}

# 測試結果統計
Write-Host "`n" + "=" * 50
Write-Host "📊 測試結果統計" -ForegroundColor Cyan

$successCount = ($testResults | Where-Object { $_.Status -eq "成功" }).Count
$failureCount = ($testResults | Where-Object { $_.Status -eq "失敗" }).Count
$errorCount = ($testResults | Where-Object { $_.Status -eq "錯誤" }).Count
$totalCount = $testResults.Count

Write-Host ""
Write-Host "總測試數: $totalCount" -ForegroundColor White
Write-Host "成功: $successCount" -ForegroundColor Green
Write-Host "失敗: $failureCount" -ForegroundColor Red
Write-Host "錯誤: $errorCount" -ForegroundColor Red

$successRate = if ($totalCount -gt 0) { [math]::Round(($successCount / $totalCount) * 100, 1) } else { 0 }
Write-Host "成功率: $successRate%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

# 詳細測試結果
if ($Verbose -or $failureCount -gt 0 -or $errorCount -gt 0) {
    Write-Host "`n📋 詳細測試結果:" -ForegroundColor Cyan
    
    foreach ($result in $testResults) {
        $statusColor = switch ($result.Status) {
            "成功" { "Green" }
            "失敗" { "Red" }
            "錯誤" { "Red" }
            default { "White" }
        }
        
        Write-Host ""
        Write-Host "  測試項目: $($result.Name)" -ForegroundColor White
        Write-Host "  狀態: $($result.Status)" -ForegroundColor $statusColor
        
        if ($result.StatusCode) {
            Write-Host "  HTTP 狀態碼: $($result.StatusCode)" -ForegroundColor Gray
        }
        
        if ($result.Error) {
            Write-Host "  錯誤訊息: $($result.Error)" -ForegroundColor Red
        }
    }
}

# 系統建議
Write-Host "`n💡 系統建議:" -ForegroundColor Cyan

if ($successRate -eq 100) {
    Write-Host "🎉 所有測試都通過了！系統運行正常。" -ForegroundColor Green
    Write-Host "   您可以開始使用多 LLM 功能了。" -ForegroundColor Green
} elseif ($successRate -ge 80) {
    Write-Host "⚠️  大部分功能正常，但有些問題需要注意。" -ForegroundColor Yellow
    Write-Host "   建議檢查失敗的測試項目。" -ForegroundColor Yellow
} else {
    Write-Host "❌ 系統存在嚴重問題，建議進行以下檢查：" -ForegroundColor Red
    Write-Host "   1. 確認所有服務都已啟動" -ForegroundColor White
    Write-Host "   2. 檢查埠號是否被占用" -ForegroundColor White
    Write-Host "   3. 查看服務器日誌" -ForegroundColor White
    Write-Host "   4. 重新啟動系統" -ForegroundColor White
}

# 常用命令提示
Write-Host "`n🔧 常用命令:" -ForegroundColor Cyan
Write-Host "  啟動系統: .\start-system.ps1 -Local" -ForegroundColor White
Write-Host "  檢查環境: .\check-setup.ps1" -ForegroundColor White
Write-Host "  查看日誌: Get-Process | Where-Object {`$_.ProcessName -like '*python*' -or `$_.ProcessName -like '*node*'}" -ForegroundColor White

# 快速診斷
if ($successRate -lt 100) {
    Write-Host "`n🩺 快速診斷:" -ForegroundColor Cyan
    
    # 檢查程序是否運行
    $pythonProcesses = Get-Process | Where-Object {$_.ProcessName -like '*python*'}
    $nodeProcesses = Get-Process | Where-Object {$_.ProcessName -like '*node*'}
    
    if ($pythonProcesses.Count -eq 0) {
        Write-Host "⚠️  未發現 Python 程序，MCP 服務器可能未啟動" -ForegroundColor Yellow
    } else {
        Write-Host "✅ 發現 $($pythonProcesses.Count) 個 Python 程序" -ForegroundColor Green
    }
    
    if ($nodeProcesses.Count -eq 0) {
        Write-Host "⚠️  未發現 Node.js 程序，Proxy 服務器可能未啟動" -ForegroundColor Yellow
    } else {
        Write-Host "✅ 發現 $($nodeProcesses.Count) 個 Node.js 程序" -ForegroundColor Green
    }
    
    # 檢查埠號
    try {
        $mcpPortUsed = netstat -ano | findstr ":$McpPort"
        $proxyPortUsed = netstat -ano | findstr ":$ProxyPort"
        
        if ($mcpPortUsed) {
            Write-Host "✅ 埠號 $McpPort (MCP) 正在使用中" -ForegroundColor Green
        } else {
            Write-Host "⚠️  埠號 $McpPort (MCP) 未被使用" -ForegroundColor Yellow
        }
        
        if ($proxyPortUsed) {
            Write-Host "✅ 埠號 $ProxyPort (Proxy) 正在使用中" -ForegroundColor Green
        } else {
            Write-Host "⚠️  埠號 $ProxyPort (Proxy) 未被使用" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️  無法檢查埠號狀態" -ForegroundColor Yellow
    }
}

Write-Host "`n測試完成！" -ForegroundColor Cyan

# 返回適當的退出碼
if ($successRate -eq 100) {
    exit 0
} elseif ($successRate -ge 80) {
    exit 1
} else {
    exit 2
}
