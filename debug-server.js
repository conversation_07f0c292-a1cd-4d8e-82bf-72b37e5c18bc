console.log('Starting debug server...');

process.on('uncaughtException', (err) => {
    console.error('Uncaught Exception:', err);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

try {
    console.log('Loading http module...');
    const http = require('http');
    console.log('HTTP module loaded successfully');
    
    console.log('Creating server...');
    const server = http.createServer((req, res) => {
        console.log('Request received:', req.url);
        res.writeHead(200, { 'Content-Type': 'text/html' });
        res.end('<h1>Debug Server Working!</h1><p>Time: ' + new Date() + '</p>');
    });
    
    console.log('Starting server on port 3000...');
    server.listen(3000, () => {
        console.log('✅ Debug server running on http://localhost:3000');
    });
    
    server.on('error', (err) => {
        console.error('❌ Server error:', err);
    });
    
} catch (error) {
    console.error('❌ Startup error:', error);
    process.exit(1);
}
