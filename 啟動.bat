@echo off
setlocal enabledelayedexpansion
title 啟動 Browser MCP 多 LLM 系統

echo.
echo ========================================
echo   啟動 Browser MCP 多 LLM 系統
echo ========================================
echo.

REM 創建錯誤日誌文件
set LOGFILE=%~dp0error.log
echo 開始執行時間: %date% %time% > "%LOGFILE%"

echo 檢查當前目錄結構...
echo 當前目錄: %cd%
dir /b >> "%LOGFILE%" 2>&1

REM 檢查必要目錄是否存在
if not exist "web-eval-agent" (
    echo 錯誤: 找不到 web-eval-agent 目錄 >> "%LOGFILE%"
    echo 錯誤: 找不到 web-eval-agent 目錄
    echo 當前目錄內容:
    dir /b
    goto error_exit
)

if not exist "prompt-proxy" (
    echo 錯誤: 找不到 prompt-proxy 目錄 >> "%LOGFILE%"
    echo 錯誤: 找不到 prompt-proxy 目錄
    echo 當前目錄內容:
    dir /b
    goto error_exit
)

echo 檢查並安裝必要工具...

REM 檢查 Python
python --version >nul 2>&1
if errorlevel 1 (
    echo 錯誤: Python 未安裝 >> "%LOGFILE%"
    echo 錯誤: Python 未安裝
    echo 請先安裝 Python: https://www.python.org/downloads/
    echo 安裝時請勾選 "Add Python to PATH"
    goto error_exit
)
echo Python 已安裝

REM 檢查 Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo 錯誤: Node.js 未安裝 >> "%LOGFILE%"
    echo 錯誤: Node.js 未安裝
    echo 請先安裝 Node.js: https://nodejs.org/
    goto error_exit
)
echo Node.js 已安裝

REM 檢查 npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo 錯誤: npm 未安裝 >> "%LOGFILE%"
    echo 錯誤: npm 未安裝
    goto error_exit
)
echo npm 已安裝

REM 安裝 uv
echo 安裝 uv...
pip install uv >> "%LOGFILE%" 2>&1
if errorlevel 1 (
    echo 警告: uv 安裝可能失敗，嘗試繼續...
)

echo.
echo 安裝依賴...

REM 安裝 Python 依賴
echo 安裝 Python 依賴...
cd web-eval-agent
if errorlevel 1 (
    echo 錯誤: 無法進入 web-eval-agent 目錄 >> "%LOGFILE%"
    echo 錯誤: 無法進入 web-eval-agent 目錄
    goto error_exit
)

uv pip install -e . >> "%LOGFILE%" 2>&1
if errorlevel 1 (
    echo uv 失敗，嘗試使用 pip...
    pip install -e . >> "%LOGFILE%" 2>&1
    if errorlevel 1 (
        echo 錯誤: Python 依賴安裝失敗 >> "%LOGFILE%"
        echo 錯誤: Python 依賴安裝失敗
        goto error_exit
    )
)
echo Python 依賴安裝完成

REM 安裝 Playwright
echo 安裝 Playwright...
uv run playwright install chromium >> "%LOGFILE%" 2>&1
if errorlevel 1 (
    python -m playwright install chromium >> "%LOGFILE%" 2>&1
    if errorlevel 1 (
        echo 警告: Playwright 安裝失敗，但系統可能仍可運行
    )
)

cd ..

REM 安裝 Node.js 依賴
echo 安裝 Node.js 依賴...
cd prompt-proxy
if errorlevel 1 (
    echo 錯誤: 無法進入 prompt-proxy 目錄 >> "%LOGFILE%"
    echo 錯誤: 無法進入 prompt-proxy 目錄
    goto error_exit
)

npm install >> "%LOGFILE%" 2>&1
if errorlevel 1 (
    echo 錯誤: Node.js 依賴安裝失敗 >> "%LOGFILE%"
    echo 錯誤: Node.js 依賴安裝失敗
    goto error_exit
)
echo Node.js 依賴安裝完成

cd ..

echo.
echo 啟動服務...

REM 啟動 MCP 服務器
echo 啟動 MCP 服務器...
cd web-eval-agent
start /B uv run python -m webEvalAgent.mcp_server --local --host 0.0.0.0 --port 5000 >> "%LOGFILE%" 2>&1
if errorlevel 1 (
    start /B python -m webEvalAgent.mcp_server --local --host 0.0.0.0 --port 5000 >> "%LOGFILE%" 2>&1
)
cd ..

REM 等待 MCP 服務器啟動
echo 等待 MCP 服務器啟動...
timeout /t 8 /nobreak >nul

REM 啟動 Proxy 服務器
echo 啟動 Proxy 服務器...
cd prompt-proxy
start /B node server.js >> "%LOGFILE%" 2>&1
cd ..

REM 等待 Proxy 服務器啟動
echo 等待 Proxy 服務器啟動...
timeout /t 5 /nobreak >nul

REM 檢查服務是否啟動
echo 檢查服務狀態...
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:5000/health' -TimeoutSec 3 | Out-Null; Write-Host 'MCP 服務器: 正常' } catch { Write-Host 'MCP 服務器: 可能未啟動' }"
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:3000/health' -TimeoutSec 3 | Out-Null; Write-Host 'Proxy 服務器: 正常' } catch { Write-Host 'Proxy 服務器: 可能未啟動' }"

REM 打開瀏覽器
echo 打開瀏覽器...
start http://localhost:3000

echo.
echo ========================================
echo   系統啟動完成！
echo ========================================
echo.
echo 服務狀態:
echo   - MCP 服務器: http://localhost:5000
echo   - Proxy 服務器: http://localhost:3000
echo   - 前端介面: http://localhost:3000
echo.
echo 使用說明:
echo   - 瀏覽器已自動打開
echo   - 選擇要使用的 LLM 模型
echo   - 輸入問題並發送到多個 AI
echo   - 關閉此視窗即可停止服務
echo.
echo 如有問題請查看 error.log 文件
echo.

pause
exit /b 0

:error_exit
echo.
echo 啟動失敗！請查看 error.log 文件了解詳細錯誤
echo 錯誤日誌位置: %LOGFILE%
echo.
pause
exit /b 1
