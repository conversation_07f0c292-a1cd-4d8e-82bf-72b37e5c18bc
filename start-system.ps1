# Browser MCP 多 LLM 系統啟動腳本
param(
    [switch]$Local,
    [switch]$Cloud,
    [string]$ApiKey = ""
)

# 預設使用離線模式
if (-not $Local -and -not $Cloud) {
    $Local = $true
}

Write-Host "🚀 Browser MCP 多 LLM 系統啟動" -ForegroundColor Cyan
Write-Host "=" * 50

$mode = if ($Local) { "離線模式" } else { "雲端模式" }
Write-Host "啟動模式: $mode" -ForegroundColor Yellow

# 檢查必要工具
Write-Host "`n🔍 檢查必要工具..." -ForegroundColor Yellow

$tools = @("python", "node", "npm", "uv")
foreach ($tool in $tools) {
    try {
        $null = & $tool --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $tool 已安裝" -ForegroundColor Green
        } else {
            throw "Command failed"
        }
    } catch {
        Write-Host "❌ $tool 未安裝或不在 PATH 中" -ForegroundColor Red
        Write-Host "請先安裝必要工具後再執行此腳本" -ForegroundColor Yellow
        exit 1
    }
}

# 設置環境變數
if ($Cloud -and $ApiKey) {
    $env:OPERATIVE_API_KEY = $ApiKey
    Write-Host "✅ 已設置 API Key" -ForegroundColor Green
}

# 安裝依賴
Write-Host "`n📦 安裝依賴..." -ForegroundColor Yellow

# 安裝 Python 依賴
Write-Host "安裝 Python 依賴..."
Set-Location "web-eval-agent"
try {
    uv pip install -e . | Out-Null
    Write-Host "✅ Python 依賴安裝完成" -ForegroundColor Green
} catch {
    Write-Host "❌ Python 依賴安裝失敗" -ForegroundColor Red
    exit 1
}

# 安裝 Playwright
Write-Host "安裝 Playwright 瀏覽器..."
try {
    uv run playwright install chromium | Out-Null
    Write-Host "✅ Playwright 安裝完成" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Playwright 安裝可能失敗，但系統仍可運行" -ForegroundColor Yellow
}

Set-Location ".."

# 安裝 Node.js 依賴
Write-Host "安裝 Node.js 依賴..."
Set-Location "prompt-proxy"
try {
    npm install | Out-Null
    Write-Host "✅ Node.js 依賴安裝完成" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js 依賴安裝失敗" -ForegroundColor Red
    exit 1
}
Set-Location ".."

# 啟動服務
Write-Host "`n🚀 啟動服務..." -ForegroundColor Yellow

# 啟動 MCP 服務器
Write-Host "啟動 MCP 服務器..."
Set-Location "web-eval-agent"

$mcpArgs = @("run", "python", "-m", "webEvalAgent.mcp_server", "--host", "0.0.0.0", "--port", "5000")
if ($Local) {
    $mcpArgs += "--local"
}

$mcpProcess = Start-Process -FilePath "uv" -ArgumentList $mcpArgs -NoNewWindow -PassThru
Write-Host "✅ MCP 服務器啟動中 (PID: $($mcpProcess.Id))" -ForegroundColor Green

Set-Location ".."

# 等待 MCP 服務器啟動
Write-Host "等待 MCP 服務器啟動..."
for ($i = 1; $i -le 30; $i++) {
    Start-Sleep -Seconds 1
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:5000/health" -TimeoutSec 2 -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ MCP 服務器啟動成功" -ForegroundColor Green
            break
        }
    } catch {
        # 繼續等待
    }
    
    if ($i -eq 30) {
        Write-Host "❌ MCP 服務器啟動超時" -ForegroundColor Red
        $mcpProcess.Kill()
        exit 1
    }
}

# 啟動 Proxy 服務器
Write-Host "啟動 Proxy 服務器..."
Set-Location "prompt-proxy"

$proxyProcess = Start-Process -FilePath "node" -ArgumentList "server.js" -NoNewWindow -PassThru
Write-Host "✅ Proxy 服務器啟動中 (PID: $($proxyProcess.Id))" -ForegroundColor Green

Set-Location ".."

# 等待 Proxy 服務器啟動
Write-Host "等待 Proxy 服務器啟動..."
for ($i = 1; $i -le 30; $i++) {
    Start-Sleep -Seconds 1
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000/health" -TimeoutSec 2 -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Proxy 服務器啟動成功" -ForegroundColor Green
            break
        }
    } catch {
        # 繼續等待
    }
    
    if ($i -eq 30) {
        Write-Host "❌ Proxy 服務器啟動超時" -ForegroundColor Red
        $mcpProcess.Kill()
        $proxyProcess.Kill()
        exit 1
    }
}

# 顯示啟動完成資訊
Write-Host "`n" + "=" * 50
Write-Host "🎉 系統啟動完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📊 服務狀態:" -ForegroundColor Cyan
Write-Host "  - MCP 服務器: http://localhost:5000 (PID: $($mcpProcess.Id))" -ForegroundColor White
Write-Host "  - Proxy 服務器: http://localhost:3000 (PID: $($proxyProcess.Id))" -ForegroundColor White
Write-Host "  - 前端介面: http://localhost:3000" -ForegroundColor White
Write-Host ""
Write-Host "💡 使用說明:" -ForegroundColor Cyan
Write-Host "  - 瀏覽器會自動打開到系統介面" -ForegroundColor White
Write-Host "  - 選擇要使用的 LLM 模型" -ForegroundColor White
Write-Host "  - 輸入問題並發送到多個 AI" -ForegroundColor White
Write-Host "  - 按 Ctrl+C 停止所有服務" -ForegroundColor White
Write-Host ""

# 自動開啟瀏覽器
try {
    Start-Process "http://localhost:3000"
    Write-Host "🌐 已自動開啟瀏覽器" -ForegroundColor Green
} catch {
    Write-Host "⚠️  無法自動開啟瀏覽器，請手動訪問 http://localhost:3000" -ForegroundColor Yellow
}

# 等待用戶中斷
Write-Host "`n按 Ctrl+C 停止所有服務..." -ForegroundColor Gray

try {
    while ($true) {
        Start-Sleep -Seconds 1
    }
} finally {
    Write-Host "`n🛑 正在停止服務..." -ForegroundColor Yellow
    
    if ($mcpProcess -and -not $mcpProcess.HasExited) {
        $mcpProcess.Kill()
        Write-Host "✅ MCP 服務器已停止" -ForegroundColor Green
    }
    
    if ($proxyProcess -and -not $proxyProcess.HasExited) {
        $proxyProcess.Kill()
        Write-Host "✅ Proxy 服務器已停止" -ForegroundColor Green
    }
    
    Write-Host "🎉 所有服務已停止" -ForegroundColor Green
}
