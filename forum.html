<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-LLM 大亂鬥 - Web Eval Agent</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .status-bar {
            background: #f8f9fa;
            padding: 15px 30px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .main-content {
            padding: 30px;
        }

        .input-section {
            margin-bottom: 30px;
        }

        .input-section label {
            display: block;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
            font-size: 1.1em;
        }

        .prompt-input {
            width: 100%;
            min-height: 120px;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            font-family: inherit;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .prompt-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .model-selection {
            margin-bottom: 30px;
        }

        .model-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .model-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .model-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .model-card.selected {
            border-color: #4facfe;
            background: #e3f2fd;
        }

        .model-card input[type="checkbox"] {
            position: absolute;
            top: 10px;
            right: 10px;
            transform: scale(1.2);
        }

        .model-name {
            font-weight: 600;
            font-size: 1.1em;
            margin-bottom: 5px;
        }

        .model-status {
            font-size: 0.9em;
            color: #666;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 30px;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 172, 254, 0.6);
        }

        .btn-primary:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .responses-section {
            margin-top: 30px;
        }

        .responses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .response-card {
            background: #f8f9fa;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .response-header {
            padding: 15px;
            font-weight: 600;
            color: white;
            text-align: center;
        }

        .response-header.chatgpt { background: #10a37f; }
        .response-header.claude { background: #ff6b35; }
        .response-header.gemini { background: #4285f4; }
        .response-header.grok { background: #1da1f2; }

        .response-content {
            padding: 20px;
            min-height: 150px;
            background: white;
            font-size: 14px;
            line-height: 1.6;
            white-space: pre-wrap;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-style: italic;
        }

        .loading::after {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #f5c6cb;
        }

        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Multi-LLM 大亂鬥</h1>
            <p>基於 Web Eval Agent - 一次輸入，四個AI同時回應</p>
            <div style="position: absolute; top: 10px; right: 20px; background: rgba(0,0,0,0.1); padding: 5px 10px; border-radius: 15px; font-size: 12px; color: #666;">
                版本 v3.1.0 - CDP連接版 (需手動登入)
            </div>
        </div>

        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>Web Eval Agent MCP 服務器</span>
            </div>
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>瀏覽器自動化就緒</span>
            </div>
        </div>

        <div class="main-content">
            <div class="input-section">
                <label for="prompt">輸入您的問題或指令：</label>
                <textarea
                    id="prompt"
                    class="prompt-input"
                    placeholder="例如：請解釋什麼是人工智慧，並舉出三個實際應用例子..."
                ></textarea>
            </div>

            <div class="model-selection">
                <label>選擇要使用的 AI 模型：</label>
                <div class="model-grid">
                    <div class="model-card selected" onclick="toggleModel('chatgpt')">
                        <input type="checkbox" id="chatgpt" checked>
                        <div class="model-name">ChatGPT</div>
                        <div class="model-status">OpenAI GPT-4</div>
                    </div>
                    <div class="model-card selected" onclick="toggleModel('claude')">
                        <input type="checkbox" id="claude" checked>
                        <div class="model-name">Claude</div>
                        <div class="model-status">Anthropic Claude</div>
                    </div>
                    <div class="model-card selected" onclick="toggleModel('gemini')">
                        <input type="checkbox" id="gemini" checked>
                        <div class="model-name">Gemini</div>
                        <div class="model-status">Google Gemini</div>
                    </div>
                    <div class="model-card selected" onclick="toggleModel('grok')">
                        <input type="checkbox" id="grok" checked>
                        <div class="model-name">Grok</div>
                        <div class="model-status">xAI Grok</div>
                    </div>
                </div>
            </div>

            <div class="action-buttons">
                <button class="btn btn-primary" id="sendBtn" onclick="sendToAllLLMs()">
                    🚀 發送到所有選中的 LLM
                </button>
                <button class="btn btn-secondary" onclick="clearAll()">
                    🗑️ 清除所有
                </button>
            </div>

            <div class="responses-section" id="responsesSection" style="display: none;">
                <h2>AI 回應結果：</h2>
                <div class="responses-grid">
                    <div class="response-card" id="chatgpt-card" style="display: none;">
                        <div class="response-header chatgpt">ChatGPT</div>
                        <div class="response-content" id="chatgpt-response">等待回應...</div>
                    </div>
                    <div class="response-card" id="claude-card" style="display: none;">
                        <div class="response-header claude">Claude</div>
                        <div class="response-content" id="claude-response">等待回應...</div>
                    </div>
                    <div class="response-card" id="gemini-card" style="display: none;">
                        <div class="response-header gemini">Gemini</div>
                        <div class="response-content" id="gemini-response">等待回應...</div>
                    </div>
                    <div class="response-card" id="grok-card" style="display: none;">
                        <div class="response-header grok">Grok</div>
                        <div class="response-content" id="grok-response">等待回應...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleModel(modelId) {
            const checkbox = document.getElementById(modelId);
            const card = checkbox.closest('.model-card');

            checkbox.checked = !checkbox.checked;

            if (checkbox.checked) {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }

            updateSendButton();
        }

        function updateSendButton() {
            const checkboxes = document.querySelectorAll('.model-card input[type="checkbox"]');
            const anyChecked = Array.from(checkboxes).some(cb => cb.checked);
            const sendBtn = document.getElementById('sendBtn');

            sendBtn.disabled = !anyChecked;
        }

        function getSelectedModels() {
            const checkboxes = document.querySelectorAll('.model-card input[type="checkbox"]:checked');
            return Array.from(checkboxes).map(cb => cb.id);
        }

        async function sendToAllLLMs() {
            const prompt = document.getElementById('prompt').value.trim();
            if (!prompt) {
                alert('請輸入問題或指令！');
                return;
            }

            const selectedModels = getSelectedModels();
            if (selectedModels.length === 0) {
                alert('請至少選擇一個 AI 模型！');
                return;
            }

            // 顯示回應區域
            document.getElementById('responsesSection').style.display = 'block';

            // 顯示選中的模型卡片並設置載入狀態
            selectedModels.forEach(model => {
                const card = document.getElementById(`${model}-card`);
                const responseDiv = document.getElementById(`${model}-response`);
                card.style.display = 'block';
                responseDiv.innerHTML = '<div class="loading">正在發送請求並等待回應</div>';
            });

            // 隱藏未選中的模型卡片
            const allModels = ['chatgpt', 'claude', 'gemini', 'grok'];
            allModels.forEach(model => {
                if (!selectedModels.includes(model)) {
                    document.getElementById(`${model}-card`).style.display = 'none';
                }
            });

            // 禁用發送按鈕
            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = true;
            sendBtn.textContent = '🔄 處理中...';

            try {
                // 調用 MCP 服務器的 send_prompt_to_llms 工具
                const response = await fetch('http://localhost:5000/send_prompt_to_llms', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        models: selectedModels
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.text();

                // 解析結果並顯示
                displayResults(result, selectedModels);

            } catch (error) {
                console.error('Error:', error);
                selectedModels.forEach(model => {
                    const responseDiv = document.getElementById(`${model}-response`);
                    responseDiv.innerHTML = `<div class="error">❌ 連接錯誤: ${error.message}<br><br>請確保：<br>1. Web Eval Agent MCP 服務器正在運行<br>2. 服務器地址為 http://localhost:5000<br>3. 網路連接正常</div>`;
                });
            } finally {
                // 恢復發送按鈕
                sendBtn.disabled = false;
                sendBtn.textContent = '🚀 發送到所有選中的 LLM';
                updateSendButton();
            }
        }

        function displayResults(resultText, selectedModels) {
            // 簡單的結果解析 - 根據 multi_llm_handler.py 的輸出格式
            const lines = resultText.split('\n');
            let currentModel = null;
            let currentResponse = '';

            for (const line of lines) {
                if (line.startsWith('## ')) {
                    // 保存前一個模型的回應
                    if (currentModel && currentResponse) {
                        updateModelResponse(currentModel.toLowerCase(), currentResponse.trim());
                    }

                    // 開始新模型
                    currentModel = line.replace('## ', '').trim();
                    currentResponse = '';
                } else if (line.startsWith('✅ Response: ')) {
                    currentResponse = line.replace('✅ Response: ', '');
                } else if (line.startsWith('❌ ') || line.startsWith('⚠️ ')) {
                    currentResponse = line;
                } else if (currentModel && line.trim()) {
                    currentResponse += '\n' + line;
                }
            }

            // 保存最後一個模型的回應
            if (currentModel && currentResponse) {
                updateModelResponse(currentModel.toLowerCase(), currentResponse.trim());
            }

            // 如果沒有解析到具體回應，顯示原始結果
            if (!currentModel) {
                selectedModels.forEach(model => {
                    updateModelResponse(model, resultText);
                });
            }
        }

        function updateModelResponse(model, response) {
            const responseDiv = document.getElementById(`${model}-response`);
            if (responseDiv) {
                if (response.startsWith('❌') || response.startsWith('⚠️')) {
                    responseDiv.innerHTML = `<div class="error">${response}</div>`;
                } else {
                    responseDiv.innerHTML = `<div class="success">${response}</div>`;
                }
            }
        }

        function clearAll() {
            document.getElementById('prompt').value = '';
            document.getElementById('responsesSection').style.display = 'none';

            // 重置所有回應
            const models = ['chatgpt', 'claude', 'gemini', 'grok'];
            models.forEach(model => {
                const responseDiv = document.getElementById(`${model}-response`);
                responseDiv.textContent = '等待回應...';
            });
        }

        // 初始化
        updateSendButton();
    </script>
</body>
</html>
