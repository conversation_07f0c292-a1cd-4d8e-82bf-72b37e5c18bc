#!/usr/bin/env python3

import asyncio
import os
import argparse
import traceback
import uuid
from enum import Enum
from webEvalAgent.src.utils import stop_log_server
from webEvalAgent.src.log_server import send_log

# Set the API key to a fake key to avoid error in backend
os.environ["ANTHROPIC_API_KEY"] = 'not_a_real_key'
os.environ["ANONYMIZED_TELEMETRY"] = 'false'

# MCP imports
from mcp.server.fastmcp import FastMCP, Context
from mcp.types import TextContent

# Import our modules
from webEvalAgent.src.api_utils import validate_api_key
from webEvalAgent.src.tool_handlers import handle_web_evaluation, handle_setup_browser_state

# Stop any existing log server to avoid conflicts
stop_log_server()

# Parse command line arguments
parser = argparse.ArgumentParser(description='Run the MCP server with browser debugging capabilities')
parser.add_argument('--host', type=str, default='0.0.0.0', help='Host address to bind to')
parser.add_argument('--port', type=int, default=5000, help='Port to listen on')
parser.add_argument('--local', action='store_true', help='Run in local mode without API key')
args = parser.parse_args()

# Create the MCP server with host and port
mcp = FastMCP("Operative", bind_address=args.host, port=args.port)

# Define the browser tools
class BrowserTools(str, Enum):
    WEB_EVAL_AGENT = "web_eval_agent"
    SETUP_BROWSER_STATE = "setup_browser_state"

# API key validation (skipped in local mode)
if not args.local:
    api_key = os.environ.get('OPERATIVE_API_KEY')
    if api_key:
        is_valid = asyncio.run(validate_api_key(api_key))
        if not is_valid:
            print("Error: Invalid API key. Please provide a valid OperativeAI API key in the OPERATIVE_API_KEY environment variable.")
            exit(1)
    else:
        print("Error: No API key provided. Please set the OPERATIVE_API_KEY environment variable or use --local mode.")
        exit(1)
else:
    api_key = None

@mcp.tool(name=BrowserTools.WEB_EVAL_AGENT)
async def web_eval_agent(url: str, task: str, ctx: Context, headless_browser: bool = False) -> list[TextContent]:
    """Evaluate the user experience / interface of a web application."""
    headless = headless_browser
    if not args.local:
        is_valid = await validate_api_key(api_key)
        if not is_valid:
            error_message_str = "❌ Error: API Key validation failed when running the tool.\n"
            error_message_str += "   Reason: Free tier limit reached.\n"
            error_message_str += "   👉 Please subscribe at https://operative.sh to continue."
            return [TextContent(type="text", text=error_message_str)]
    try:
        tool_call_id = str(uuid.uuid4())
        return await handle_web_evaluation(
            {"url": url, "task": task, "headless": headless, "tool_call_id": tool_call_id},
            ctx,
            api_key
        )
    except Exception as e:
        tb = traceback.format_exc()
        return [TextContent(
            type="text",
            text=f"Error executing web_eval_agent: {str(e)}\n\nTraceback:\n{tb}"
        )]

@mcp.tool(name=BrowserTools.SETUP_BROWSER_STATE)
async def setup_browser_state(url: str = None, ctx: Context = None) -> list[TextContent]:
    """Sets up and saves browser state for future use."""
    if not args.local:
        is_valid = await validate_api_key(api_key)
        if not is_valid:
            error_message_str = "❌ Error: API Key validation failed when running the tool.\n"
            error_message_str += "   Reason: Free tier limit reached.\n"
            error_message_str += "   👉 Please subscribe at https://operative.sh to continue."
            return [TextContent(type="text", text=error_message_str)]
    try:
        tool_call_id = str(uuid.uuid4())
        send_log(f"Generated new tool_call_id for setup_browser_state: {tool_call_id}")
        return await handle_setup_browser_state(
            {"url": url, "tool_call_id": tool_call_id},
            ctx,
            api_key
        )
    except Exception as e:
        tb = traceback.format_exc()
        return [TextContent(
            type="text",
            text=f"Error executing setup_browser_state: {str(e)}\n\nTraceback:\n{tb}"
        )]

def main():
    try:
        # Run the FastMCP server with stdio transport
        mcp.run(transport='stdio')
    finally:
        # Cleanup resources
        pass

if __name__ == "__main__":
    main()
