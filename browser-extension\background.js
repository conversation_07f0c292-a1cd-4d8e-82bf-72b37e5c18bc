// Browser MCP Multi-LLM Background Script

class MultiLLMController {
    constructor() {
        this.activePrompt = null;
        this.responses = {};
        this.tabStates = {};
        this.forumTabId = null;
        
        this.init();
    }
    
    init() {
        // 監聽來自content scripts的訊息
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // 保持訊息通道開啟
        });
        
        // 監聽來自forum頁面的連接
        chrome.runtime.onConnect.addListener((port) => {
            if (port.name === 'forum-connection') {
                this.handleForumConnection(port);
            }
        });
        
        // 監聽分頁更新
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdate(tabId, changeInfo, tab);
        });
    }
    
    handleMessage(message, sender, sendResponse) {
        console.log('Background received message:', message);
        
        switch (message.type) {
            case 'CONTENT_SCRIPT_READY':
                this.handleContentScriptReady(message, sender);
                break;
                
            case 'RESPONSE_CHUNK':
                this.handleResponseChunk(message, sender);
                break;
                
            case 'RESPONSE_COMPLETE':
                this.handleResponseComplete(message, sender);
                break;
                
            case 'ERROR':
                this.handleError(message, sender);
                break;
                
            case 'GET_TAB_STATES':
                sendResponse(this.getTabStates());
                break;
        }
    }
    
    handleForumConnection(port) {
        console.log('Forum connected');
        this.forumPort = port;
        
        port.onMessage.addListener((message) => {
            switch (message.type) {
                case 'SEND_PROMPT':
                    this.sendPromptToAllTabs(message.prompt, message.models);
                    break;
                    
                case 'GET_TAB_STATUS':
                    port.postMessage({
                        type: 'TAB_STATUS',
                        data: this.getTabStates()
                    });
                    break;
            }
        });
        
        port.onDisconnect.addListener(() => {
            console.log('Forum disconnected');
            this.forumPort = null;
        });
    }
    
    async sendPromptToAllTabs(prompt, selectedModels) {
        console.log('Sending prompt to models:', selectedModels);
        this.activePrompt = prompt;
        this.responses = {};
        
        const aiSites = {
            'chatgpt': 'https://chat.openai.com',
            'claude': 'https://claude.ai', 
            'gemini': 'https://gemini.google.com',
            'grok': 'https://grok.x.ai'
        };
        
        // 通知forum開始處理
        if (this.forumPort) {
            this.forumPort.postMessage({
                type: 'PROMPT_STARTED',
                data: { prompt, models: selectedModels }
            });
        }
        
        // 找到或創建對應的分頁
        for (const model of selectedModels) {
            const siteUrl = aiSites[model];
            if (!siteUrl) continue;
            
            try {
                const tab = await this.findOrCreateTab(siteUrl, model);
                await this.sendPromptToTab(tab.id, prompt, model);
            } catch (error) {
                console.error(`Error sending prompt to ${model}:`, error);
                this.handleError({
                    type: 'ERROR',
                    model: model,
                    error: error.message
                });
            }
        }
    }
    
    async findOrCreateTab(url, model) {
        // 查找現有分頁
        const tabs = await chrome.tabs.query({ url: url + '*' });
        
        if (tabs.length > 0) {
            // 使用現有分頁
            const tab = tabs[0];
            await chrome.tabs.update(tab.id, { active: false }); // 不要切換到該分頁
            return tab;
        } else {
            // 創建新分頁
            const tab = await chrome.tabs.create({ 
                url: url, 
                active: false // 在背景開啟
            });
            
            // 等待分頁載入完成
            return new Promise((resolve) => {
                const listener = (tabId, changeInfo) => {
                    if (tabId === tab.id && changeInfo.status === 'complete') {
                        chrome.tabs.onUpdated.removeListener(listener);
                        resolve(tab);
                    }
                };
                chrome.tabs.onUpdated.addListener(listener);
            });
        }
    }
    
    async sendPromptToTab(tabId, prompt, model) {
        const maxRetries = 3;
        let retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                await chrome.tabs.sendMessage(tabId, {
                    type: 'SEND_PROMPT',
                    prompt: prompt,
                    model: model
                });
                return; // 成功發送，退出重試循環

            } catch (error) {
                retryCount++;
                console.error(`Failed to send message to tab ${tabId} (attempt ${retryCount}):`, error);

                if (retryCount >= maxRetries) {
                    // 最後一次重試失敗，檢查分頁狀態
                    try {
                        const tab = await chrome.tabs.get(tabId);
                        if (tab.status !== 'complete') {
                            throw new Error(`分頁未完全載入 (狀態: ${tab.status})`);
                        }
                    } catch (tabError) {
                        throw new Error(`分頁不存在或無法訪問: ${tabError.message}`);
                    }

                    throw new Error(`發送失敗，已重試${maxRetries}次: ${error.message}`);
                }

                // 等待後重試
                await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
            }
        }
    }
    
    handleContentScriptReady(message, sender) {
        const model = message.model;
        const tabId = sender.tab.id;
        
        this.tabStates[model] = {
            tabId: tabId,
            status: 'ready',
            url: sender.tab.url
        };
        
        console.log(`${model} content script ready on tab ${tabId}`);
    }
    
    handleResponseChunk(message, sender) {
        const model = message.model;
        const chunk = message.chunk;
        
        if (!this.responses[model]) {
            this.responses[model] = '';
        }
        
        this.responses[model] += chunk;
        
        // 轉發給forum
        if (this.forumPort) {
            this.forumPort.postMessage({
                type: 'RESPONSE_CHUNK',
                data: {
                    model: model,
                    chunk: chunk,
                    fullResponse: this.responses[model]
                }
            });
        }
    }
    
    handleResponseComplete(message, sender) {
        const model = message.model;
        
        if (this.tabStates[model]) {
            this.tabStates[model].status = 'complete';
        }
        
        // 轉發給forum
        if (this.forumPort) {
            this.forumPort.postMessage({
                type: 'RESPONSE_COMPLETE',
                data: {
                    model: model,
                    response: this.responses[model] || message.response
                }
            });
        }
    }
    
    handleError(message, sender) {
        const model = message.model;
        
        if (this.tabStates[model]) {
            this.tabStates[model].status = 'error';
            this.tabStates[model].error = message.error;
        }
        
        // 轉發給forum
        if (this.forumPort) {
            this.forumPort.postMessage({
                type: 'ERROR',
                data: {
                    model: model,
                    error: message.error
                }
            });
        }
    }
    
    handleTabUpdate(tabId, changeInfo, tab) {
        // 更新分頁狀態
        for (const [model, state] of Object.entries(this.tabStates)) {
            if (state.tabId === tabId) {
                state.url = tab.url;
                state.title = tab.title;
                break;
            }
        }
    }
    
    getTabStates() {
        return this.tabStates;
    }
}

// 初始化控制器
const controller = new MultiLLMController();
