import platform
import subprocess

def stop_log_server():
     """Stop the log server on port 5009.

     This function attempts to stop any process running on port 5009
     by killing the process if it's a Unix-like system, or using taskkill
     on Windows.
     """
     try:
         if platform.system() == "Windows":
             # Get processes using port 5009
             result = subprocess.run(["netstat", "-ano"], capture_output=True, text=True)
             if result.returncode == 0:
                 lines = result.stdout.split('\n')
                 for line in lines:
                     if ':5009' in line and 'LISTENING' in line:
                         parts = line.split()
                         if len(parts) >= 5:
                             pid = parts[-1]
                             subprocess.run(["taskkill", "/F", "/PID", pid],
                                          stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL)
                             break
         else:  # Unix-like systems (Linux, macOS)
             subprocess.run("kill $(lsof -ti tcp:5009)", shell=True,
                             stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL)
     except Exception:
         pass  # Ignore errors if no process is running on that port