# Browser MCP 本地啟動指南

## 1. 環境準備
- 作業系統：Windows 10/11
- PowerShell 5.1+
- Python 3.11+
- 安裝 uv 工具：
  ```powershell
  irm https://astral.sh/uv/install.ps1 | iex
  ```

## 2. 五步安裝流程

### 步驟 1：進入專案目錄
```powershell
cd "C:\Users\<USER>\OneDrive\Windsurf-202412\202506-LLM大亂鬥\web-eval-agent"
```

### 步驟 2：建立虛擬環境
```powershell
uv venv .venv
```

### 步驟 3：安裝專案
```powershell
uv pip install -e .
```

### 步驟 4：設定 API 金鑰（擇一）
- **雲端模式**（需金鑰）：
  ```powershell
  $Env:OPERATIVE_API_KEY="op-xxxxxxxxxxxxx"
  ```
- **離線模式**（無需金鑰）：
  啟動時加上 `--local` 參數

### 步驟 5：啟動 MCP 伺服器
```powershell
uv run python -m webEvalAgent.mcp_server --host 0.0.0.0 --port 5000
```

## 3. 健康檢查
```powershell
curl http://localhost:5000/health
```
預期輸出：`{"status":"ok"}`

## 4. 常見問題排除

| 錯誤訊息 | 解決方案 |
|---------|----------|
| `uv : command not found` | 重新開啟終端機或重新執行安裝腳本 |
| `ModuleNotFoundError: webEvalAgent` | 確認位於含 `pyproject.toml` 的目錄執行安裝 |
| `EADDRINUSE :5000` | 執行 `netstat -ano | findstr :5000` 後 `taskkill /PID <ID> /F` |
| `Missing OPERATIVE_API_KEY` | 設定環境變數或使用 `--local` 模式 |

## 5. 進階選項
- 變更監聽埠：`--port 5001`
- 限制本地存取：`--host 127.0.0.1`
- 離線模式範例：
  ```powershell
  uv run python -m webEvalAgent.mcp_server --local --port 5001
  ```

## 6. 文件更新記錄
- 2025/06/29 - 初版發布