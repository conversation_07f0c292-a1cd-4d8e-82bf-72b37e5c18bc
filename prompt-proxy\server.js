#!/usr/bin/env node

import express from 'express';
import cors from 'cors';
import axios from 'axios';
import { WebSocketServer } from 'ws';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import compression from 'compression';
import { createServer } from 'http';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3000;
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://localhost:5000';

// Middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'", "'unsafe-inline'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'", "ws:", "wss:"]
        }
    }
}));

app.use(compression());
app.use(cors({
    origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
    credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// Serve static files
app.use(express.static(path.join(__dirname, '../frontend')));

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        mcpServer: MCP_SERVER_URL
    });
});

// MCP Server health check
app.get('/api/mcp-health', async (req, res) => {
    try {
        const response = await axios.get(`${MCP_SERVER_URL}/health`, {
            timeout: 5000
        });
        res.json({
            status: 'ok',
            mcpServer: response.data
        });
    } catch (error) {
        console.error('MCP server health check failed:', error.message);
        res.status(503).json({
            status: 'error',
            message: 'MCP server unavailable',
            error: error.message
        });
    }
});

// Send prompt to multiple LLMs
app.post('/api/send-prompt', async (req, res) => {
    try {
        const { prompt, models } = req.body;
        
        if (!prompt || typeof prompt !== 'string' || prompt.trim().length === 0) {
            return res.status(400).json({
                error: 'Invalid prompt provided'
            });
        }

        const validModels = ['chatgpt', 'claude', 'gemini', 'grok'];
        const selectedModels = models && Array.isArray(models) 
            ? models.filter(m => validModels.includes(m))
            : validModels;

        if (selectedModels.length === 0) {
            return res.status(400).json({
                error: 'No valid models selected'
            });
        }

        console.log(`Sending prompt to models: ${selectedModels.join(', ')}`);
        console.log(`Prompt: ${prompt.substring(0, 100)}...`);

        // Call MCP server
        const mcpResponse = await axios.post(`${MCP_SERVER_URL}/call`, {
            method: 'tools/call',
            params: {
                name: 'send_prompt_to_llms',
                arguments: {
                    prompt: prompt,
                    models: selectedModels
                }
            }
        }, {
            timeout: 60000, // 60 second timeout for LLM responses
            headers: {
                'Content-Type': 'application/json'
            }
        });

        res.json({
            success: true,
            data: mcpResponse.data,
            models: selectedModels,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Error sending prompt to LLMs:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            return res.status(503).json({
                error: 'MCP server is not running',
                message: 'Please start the MCP server first'
            });
        }
        
        if (error.response) {
            return res.status(error.response.status).json({
                error: 'MCP server error',
                message: error.response.data?.message || error.message
            });
        }

        res.status(500).json({
            error: 'Internal server error',
            message: error.message
        });
    }
});

// Get browser tabs
app.get('/api/browser-tabs', async (req, res) => {
    try {
        const mcpResponse = await axios.post(`${MCP_SERVER_URL}/call`, {
            method: 'tools/call',
            params: {
                name: 'get_browser_tabs',
                arguments: {}
            }
        }, {
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json'
            }
        });

        res.json({
            success: true,
            data: mcpResponse.data
        });

    } catch (error) {
        console.error('Error getting browser tabs:', error.message);
        res.status(500).json({
            error: 'Failed to get browser tabs',
            message: error.message
        });
    }
});

// Close browser tab
app.delete('/api/browser-tabs/:tabId', async (req, res) => {
    try {
        const { tabId } = req.params;
        
        const mcpResponse = await axios.post(`${MCP_SERVER_URL}/call`, {
            method: 'tools/call',
            params: {
                name: 'close_browser_tab',
                arguments: {
                    tab_id: tabId
                }
            }
        }, {
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json'
            }
        });

        res.json({
            success: true,
            data: mcpResponse.data
        });

    } catch (error) {
        console.error('Error closing browser tab:', error.message);
        res.status(500).json({
            error: 'Failed to close browser tab',
            message: error.message
        });
    }
});

// Serve the main HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/forum.html'));
});

// Create HTTP server
const server = createServer(app);

// WebSocket server for real-time updates
const wss = new WebSocketServer({ server });

wss.on('connection', (ws) => {
    console.log('New WebSocket connection established');
    
    ws.on('message', async (message) => {
        try {
            const data = JSON.parse(message.toString());
            
            if (data.type === 'ping') {
                ws.send(JSON.stringify({ type: 'pong' }));
                return;
            }
            
            if (data.type === 'send_prompt') {
                // Handle real-time prompt sending
                ws.send(JSON.stringify({
                    type: 'prompt_status',
                    status: 'processing',
                    message: 'Sending prompt to LLMs...'
                }));
                
                // This could be extended to provide real-time updates
                // during the LLM response generation process
            }
            
        } catch (error) {
            console.error('WebSocket message error:', error);
            ws.send(JSON.stringify({
                type: 'error',
                message: 'Invalid message format'
            }));
        }
    });
    
    ws.on('close', () => {
        console.log('WebSocket connection closed');
    });
    
    ws.on('error', (error) => {
        console.error('WebSocket error:', error);
    });
});

// Error handling middleware
app.use((error, req, res, next) => {
    console.error('Express error:', error);
    res.status(500).json({
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        error: 'Not found',
        message: `Route ${req.method} ${req.path} not found`
    });
});

// Start server
server.listen(PORT, () => {
    console.log(`🚀 Prompt Proxy Server running on http://localhost:${PORT}`);
    console.log(`📡 MCP Server URL: ${MCP_SERVER_URL}`);
    console.log(`🌐 WebSocket Server: ws://localhost:${PORT}`);
    console.log(`📁 Serving static files from: ${path.join(__dirname, '../frontend')}`);
    
    // Test MCP server connection
    axios.get(`${MCP_SERVER_URL}/health`)
        .then(response => {
            console.log('✅ MCP Server connection successful');
        })
        .catch(error => {
            console.log('⚠️  MCP Server not available - make sure it\'s running');
            console.log('   Start with: uv run python -m webEvalAgent.mcp_server --transport http --local');
        });
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});
