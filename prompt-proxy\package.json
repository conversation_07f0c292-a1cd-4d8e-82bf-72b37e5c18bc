{"name": "browser-mcp-prompt-proxy", "version": "1.0.0", "description": "Prompt Proxy server for Browser MCP Multi-LLM system", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mcp", "browser", "llm", "proxy", "chatgpt", "claude", "gemini", "grok"], "author": "Browser MCP Team", "license": "MIT", "dependencies": {"axios": "^1.6.2", "compression": "^1.7.4", "cors": "^2.8.5", "express": "^4.21.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}