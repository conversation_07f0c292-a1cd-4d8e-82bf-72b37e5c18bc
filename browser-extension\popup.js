// Popup Script for Browser MCP Extension

document.addEventListener('DOMContentLoaded', function() {
    // 綁定按鈕事件
    document.getElementById('open-forum').addEventListener('click', openForumPage);
    document.getElementById('refresh-status').addEventListener('click', refreshStatus);

    // 添加健康檢查按鈕事件（如果存在）
    const healthCheckBtn = document.getElementById('health-check');
    if (healthCheckBtn) {
        healthCheckBtn.addEventListener('click', performHealthCheck);
    }

    // 初始化狀態
    refreshStatus();
});

function openForumPage() {
    // 開啟forum.html頁面
    const forumUrl = chrome.runtime.getURL('../frontend/forum.html');
    chrome.tabs.create({ url: forumUrl });
    window.close();
}

function refreshStatus() {
    // 向background script請求狀態更新
    chrome.runtime.sendMessage({ type: 'GET_TAB_STATES' }, (response) => {
        if (response) {
            updateStatusDisplay(response);
        }
    });
    
    // 檢查各個AI網站的分頁狀態
    checkAISites();
}

function updateStatusDisplay(tabStates) {
    const models = ['chatgpt', 'claude', 'gemini', 'grok'];
    
    models.forEach(model => {
        const statusElement = document.getElementById(`${model}-status`);
        const state = tabStates[model];
        
        if (state && state.status === 'ready') {
            statusElement.textContent = '線上';
            statusElement.className = 'status-indicator status-online';
        } else {
            statusElement.textContent = '離線';
            statusElement.className = 'status-indicator status-offline';
        }
    });
}

async function checkAISites() {
    const aiSites = {
        'chatgpt': 'https://chat.openai.com',
        'claude': 'https://claude.ai',
        'gemini': 'https://gemini.google.com',
        'grok': 'https://grok.x.ai'
    };
    
    for (const [model, url] of Object.entries(aiSites)) {
        try {
            const tabs = await chrome.tabs.query({ url: url + '*' });
            const statusElement = document.getElementById(`${model}-status`);
            
            if (tabs.length > 0) {
                // 檢查分頁是否活躍
                const activeTab = tabs.find(tab => !tab.discarded);
                if (activeTab) {
                    statusElement.textContent = '已開啟';
                    statusElement.className = 'status-indicator status-online';
                } else {
                    statusElement.textContent = '已暫停';
                    statusElement.className = 'status-indicator status-offline';
                }
            } else {
                statusElement.textContent = '未開啟';
                statusElement.className = 'status-indicator status-offline';
            }
        } catch (error) {
            console.error(`Error checking ${model} status:`, error);
        }
    }
}

async function performHealthCheck() {
    const button = document.getElementById('health-check');
    if (!button) return;

    const originalText = button.textContent;
    button.textContent = '檢查中...';
    button.disabled = true;

    try {
        // 執行詳細的健康檢查
        const results = await checkDetailedStatus();
        displayDetailedResults(results);

    } catch (error) {
        console.error('Health check failed:', error);
        showError('健康檢查失敗: ' + error.message);
    } finally {
        button.textContent = originalText;
        button.disabled = false;
    }
}

async function checkDetailedStatus() {
    const aiSites = {
        'chatgpt': {
            url: 'https://chat.openai.com',
            inputSelectors: ['#prompt-textarea', 'textarea[data-id="root"]'],
            sendSelectors: ['button[data-testid="send-button"]'],
            loginSelectors: ['.auth-form', '[data-testid="login-button"]']
        },
        'claude': {
            url: 'https://claude.ai',
            inputSelectors: ['div[contenteditable="true"]', '.ProseMirror'],
            sendSelectors: ['button[aria-label="Send Message"]'],
            loginSelectors: ['.login-form', '.auth-container']
        },
        'gemini': {
            url: 'https://gemini.google.com',
            inputSelectors: ['rich-textarea[aria-label*="Enter a prompt"]'],
            sendSelectors: ['button[aria-label="Send message"]'],
            loginSelectors: ['.signin-container']
        },
        'grok': {
            url: 'https://grok.x.ai',
            inputSelectors: ['textarea[placeholder*="Ask Grok"]'],
            sendSelectors: ['button[aria-label="Send"]'],
            loginSelectors: ['.login-form', '.auth-form']
        }
    };

    const results = {};

    for (const [model, config] of Object.entries(aiSites)) {
        results[model] = await checkSiteHealth(model, config);
    }

    return results;
}

async function checkSiteHealth(model, config) {
    try {
        const tabs = await chrome.tabs.query({ url: config.url + '*' });

        if (tabs.length === 0) {
            return { status: 'no_tab', message: '分頁未開啟' };
        }

        const tab = tabs[0];
        if (tab.status !== 'complete') {
            return { status: 'loading', message: '載入中' };
        }

        // 檢查頁面元素
        const result = await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            func: checkPageElements,
            args: [config]
        });

        return result[0].result;

    } catch (error) {
        return { status: 'error', message: error.message };
    }
}

function checkPageElements(config) {
    const findElement = (selectors) => {
        for (const selector of selectors) {
            if (document.querySelector(selector)) return true;
        }
        return false;
    };

    const hasInput = findElement(config.inputSelectors);
    const hasSend = findElement(config.sendSelectors);
    const needsLogin = findElement(config.loginSelectors);

    if (needsLogin) {
        return { status: 'needs_login', message: '需要登入' };
    }

    if (!hasInput) {
        return { status: 'no_input', message: '找不到輸入框' };
    }

    if (!hasSend) {
        return { status: 'no_send', message: '找不到發送按鈕' };
    }

    return { status: 'ready', message: '準備就緒' };
}

function displayDetailedResults(results) {
    const models = ['chatgpt', 'claude', 'gemini', 'grok'];

    models.forEach(model => {
        const statusElement = document.getElementById(`${model}-status`);
        if (statusElement && results[model]) {
            const result = results[model];
            statusElement.textContent = result.message;

            // 設置狀態樣式
            if (result.status === 'ready') {
                statusElement.className = 'status-indicator status-online';
            } else {
                statusElement.className = 'status-indicator status-offline';
            }
        }
    });
}

function showError(message) {
    console.error(message);
    // 可以在這裡添加UI錯誤顯示
}
