@echo off
title 測試腳本

echo 測試腳本開始執行...
echo 當前目錄: %cd%
echo.

echo 檢查目錄結構:
dir /b

echo.
echo 檢查 web-eval-agent 目錄:
if exist "web-eval-agent" (
    echo ✅ web-eval-agent 目錄存在
) else (
    echo ❌ web-eval-agent 目錄不存在
)

echo.
echo 檢查 prompt-proxy 目錄:
if exist "prompt-proxy" (
    echo ✅ prompt-proxy 目錄存在
) else (
    echo ❌ prompt-proxy 目錄不存在
)

echo.
echo 檢查 Python:
python --version
if errorlevel 1 (
    echo ❌ Python 未安裝或不在 PATH 中
) else (
    echo ✅ Python 已安裝
)

echo.
echo 檢查 Node.js:
node --version
if errorlevel 1 (
    echo ❌ Node.js 未安裝或不在 PATH 中
) else (
    echo ✅ Node.js 已安裝
)

echo.
echo 檢查 npm:
npm --version
if errorlevel 1 (
    echo ❌ npm 未安裝或不在 PATH 中
) else (
    echo ✅ npm 已安裝
)

echo.
echo 測試完成！
pause
