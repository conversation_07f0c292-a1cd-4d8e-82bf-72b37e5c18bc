<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser MCP 多 LLM 大亂鬥</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🤖</text></svg>">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>🤖 Browser MCP 多 LLM 大亂鬥</h1>
            <p class="subtitle">一次 prompt，四個 AI 同時回應</p>
            <div class="status-bar">
                <div class="status-item">
                    <span class="status-label">Proxy 狀態:</span>
                    <span id="proxy-status" class="status-value">檢查中...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">MCP 狀態:</span>
                    <span id="mcp-status" class="status-value">檢查中...</span>
                </div>
            </div>
        </header>

        <!-- Input Section -->
        <section class="input-section">
            <div class="prompt-container">
                <label for="prompt-input" class="prompt-label">
                    💬 輸入您的 Prompt
                </label>
                <textarea 
                    id="prompt-input" 
                    class="prompt-input" 
                    placeholder="例如：請解釋量子計算的基本原理，並舉出實際應用例子..."
                    rows="4"
                ></textarea>
                
                <div class="model-selection">
                    <label class="model-label">🎯 選擇 LLM 模型:</label>
                    <div class="model-checkboxes">
                        <label class="checkbox-label">
                            <input type="checkbox" id="model-chatgpt" value="chatgpt" checked>
                            <span class="checkmark"></span>
                            ChatGPT
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="model-claude" value="claude" checked>
                            <span class="checkmark"></span>
                            Claude
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="model-gemini" value="gemini" checked>
                            <span class="checkmark"></span>
                            Gemini
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="model-grok" value="grok" checked>
                            <span class="checkmark"></span>
                            Grok
                        </label>
                    </div>
                </div>
                
                <div class="button-group">
                    <button id="send-button" class="send-button">
                        <span class="button-icon">🚀</span>
                        發送到所有 LLM
                    </button>
                    <button id="clear-button" class="clear-button">
                        <span class="button-icon">🗑️</span>
                        清除
                    </button>
                </div>
            </div>
        </section>

        <!-- Results Section -->
        <section class="results-section">
            <div class="results-header">
                <h2>📊 LLM 回應結果</h2>
                <div class="results-controls">
                    <button id="export-button" class="export-button" disabled>
                        <span class="button-icon">📄</span>
                        匯出結果
                    </button>
                    <button id="compare-button" class="compare-button" disabled>
                        <span class="button-icon">⚖️</span>
                        比較分析
                    </button>
                </div>
            </div>
            
            <div id="loading-indicator" class="loading-indicator" style="display: none;">
                <div class="spinner"></div>
                <p>正在發送 prompt 到各個 LLM...</p>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>
            
            <div id="results-container" class="results-container">
                <!-- Results will be dynamically inserted here -->
            </div>
        </section>

        <!-- Browser Tabs Management -->
        <section class="tabs-section">
            <div class="tabs-header">
                <h3>🌐 瀏覽器分頁管理</h3>
                <button id="refresh-tabs" class="refresh-button">
                    <span class="button-icon">🔄</span>
                    重新整理
                </button>
            </div>
            <div id="tabs-container" class="tabs-container">
                <!-- Browser tabs info will be displayed here -->
            </div>
        </section>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Modal for Compare Analysis -->
    <div id="compare-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>⚖️ LLM 回應比較分析</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div id="compare-content">
                    <!-- Comparison content will be inserted here -->
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
