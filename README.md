# 🤖 Browser MCP 多 LLM 大亂鬥

> **一次 prompt，四個 AI 同時回應！**

這是一個基於 Browser MCP 的多 LLM 同步驅動系統，讓您可以在一個介面中同時向 ChatGPT、Claude、Gemini、<PERSON>rok 發送相同的 prompt，並比較它們的回應。

## 🎯 系統特色

- **🚀 一鍵發送**: 單次輸入 prompt，自動發送到多個 LLM
- **🔄 即時同步**: 同時控制 4 個瀏覽器分頁，實現真正的並行處理
- **📊 結果比較**: 直觀展示各個 LLM 的回應，方便比較分析
- **🌐 Web 介面**: 現代化的 Web UI，支援響應式設計
- **⚡ 快速部署**: 一鍵啟動腳本，30 秒內完成部署
- **🔧 靈活配置**: 支援離線模式和雲端模式

## 🏗️ 系統架構

```
forum.html → Prompt Proxy (Express:3000) → Browser MCP (FastMCP:5000) → 4 個 LLM 分頁
```

### 核心組件

1. **前端介面** (`frontend/forum.html`)
   - 現代化 Web UI
   - 支援多模型選擇
   - 即時狀態顯示
   - 結果比較分析

2. **Prompt Proxy** (`prompt-proxy/server.js`)
   - Express.js 中間層
   - HTTP API 轉發
   - WebSocket 即時通訊
   - 錯誤處理與重試

3. **Browser MCP** (`web-eval-agent/`)
   - FastMCP 服務器
   - Playwright 瀏覽器控制
   - 多分頁管理
   - LLM 網站自動化

## 🚀 快速開始

### 前置需求

- Windows 10+ (PowerShell 5.1+)
- Python 3.11+
- Node.js 18+
- 網路連線

### 一鍵啟動

```powershell
# 克隆專案
git clone <repository-url>
cd browser-mcp-multi-llm

# 離線模式啟動（推薦用於開發）
.\start-system.ps1 -Local

# 雲端模式啟動（需要 API Key）
.\start-system.ps1 -Cloud -ApiKey "your-operative-api-key"
```

### 手動安裝

如果一鍵啟動失敗，可以按照以下步驟手動安裝：

```powershell
# 1. 安裝 uv 包管理器
irm https://astral.sh/uv/install.ps1 | iex

# 2. 安裝 Python 依賴
cd web-eval-agent
uv venv .venv
uv pip install -e .
uv run playwright install chromium
cd ..

# 3. 安裝 Node.js 依賴
cd prompt-proxy
npm install
cd ..

# 4. 啟動 MCP 服務器
cd web-eval-agent
uv run python -m webEvalAgent.mcp_server --transport http --local --port 5000
# 在新的終端視窗中執行

# 5. 啟動 Proxy 服務器
cd prompt-proxy
npm start
# 在新的終端視窗中執行

# 6. 開啟瀏覽器
start http://localhost:3000
```

## 📖 使用指南

### 基本使用

1. **開啟系統**
   ```powershell
   .\start-system.ps1 -Local
   ```

2. **訪問介面**
   - 自動開啟瀏覽器，或手動訪問 `http://localhost:3000`

3. **發送 Prompt**
   - 在文字框中輸入您的 prompt
   - 選擇要使用的 LLM 模型（預設全選）
   - 點擊「🚀 發送到所有 LLM」按鈕

4. **查看結果**
   - 等待各個 LLM 的回應
   - 在結果區域查看並比較回應
   - 使用「📄 匯出結果」保存結果

### 進階功能

- **⚖️ 比較分析**: 點擊比較按鈕查看詳細分析
- **🌐 分頁管理**: 查看和管理瀏覽器分頁
- **🔄 即時更新**: WebSocket 連接提供即時狀態更新

## 🔧 配置選項

### 命令列參數

```powershell
# 基本選項
.\start-system.ps1 -Local                    # 離線模式
.\start-system.ps1 -Cloud -ApiKey "key"     # 雲端模式

# 進階選項
.\start-system.ps1 -Local -McpPort 5001 -ProxyPort 3001  # 自定義埠號
.\start-system.ps1 -Help                     # 顯示幫助
```

### 環境變數

```powershell
$env:OPERATIVE_API_KEY = "your-api-key"     # OperativeAI API Key
$env:MCP_SERVER_URL = "http://localhost:5000"  # MCP 服務器 URL
```

## 🧪 測試與診斷

### 系統測試

```powershell
# 基本測試
.\test-system.ps1

# 詳細測試
.\test-system.ps1 -Verbose

# 環境檢查
.\check-setup.ps1
```

### 常見問題

#### 1. 埠號被占用
```powershell
# 查看占用程序
netstat -ano | findstr :5000
netstat -ano | findstr :3000

# 終止程序
taskkill /PID <PID> /F
```

#### 2. MCP 服務器啟動失敗
```powershell
# 檢查 Python 環境
python --version
uv --version

# 重新安裝依賴
cd web-eval-agent
uv pip install -e .
```

#### 3. 瀏覽器自動化問題
```powershell
# 重新安裝 Playwright
uv run playwright install chromium --with-deps
```

## 📊 支援的 LLM

| LLM | 網站 | 狀態 | 備註 |
|-----|------|------|------|
| ChatGPT | https://chatgpt.com | ✅ 支援 | 需要登入 |
| Claude | https://claude.ai | ✅ 支援 | 需要登入 |
| Gemini | https://gemini.google.com | ✅ 支援 | 需要登入 |
| Grok | https://x.com/i/grok | ✅ 支援 | 需要 X 帳號 |

## 🛠️ 開發指南

### 專案結構

```
browser-mcp-multi-llm/
├── web-eval-agent/           # MCP 服務器
│   ├── webEvalAgent/
│   │   ├── mcp_server.py     # 主服務器
│   │   └── src/
│   │       ├── multi_llm_handler.py  # 多 LLM 處理器
│   │       └── ...
│   └── pyproject.toml
├── prompt-proxy/             # Proxy 服務器
│   ├── server.js             # Express 服務器
│   └── package.json
├── frontend/                 # 前端介面
│   ├── forum.html            # 主頁面
│   ├── styles.css            # 樣式
│   └── app.js                # JavaScript 邏輯
├── start-system.ps1          # 啟動腳本
├── test-system.ps1           # 測試腳本
└── check-setup.ps1           # 環境檢查
```

### 添加新的 LLM

1. 在 `multi_llm_handler.py` 中添加 LLM 配置
2. 更新前端的模型選項
3. 測試新 LLM 的選擇器和互動邏輯

### API 端點

- `GET /health` - 健康檢查
- `POST /api/send-prompt` - 發送 prompt
- `GET /api/browser-tabs` - 獲取瀏覽器分頁
- `DELETE /api/browser-tabs/:id` - 關閉分頁

## 📈 效能指標

- **並發處理**: 支援 4 個 LLM 同時處理
- **回應時間**: 通常 5-15 秒（取決於 LLM 回應速度）
- **記憶體使用**: 約 1-2GB（包含瀏覽器實例）
- **成功率**: 在良好網路環境下 >90%

## 🤝 貢獻指南

1. Fork 專案
2. 創建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 開啟 Pull Request

## 📄 授權

本專案採用 MIT 授權 - 詳見 [LICENSE](LICENSE) 文件

## 🙏 致謝

- [FastMCP](https://github.com/jlowin/fastmcp) - MCP 服務器框架
- [Playwright](https://playwright.dev/) - 瀏覽器自動化
- [browser-use](https://github.com/gregpr07/browser-use) - 瀏覽器使用庫
- [Express.js](https://expressjs.com/) - Web 服務器框架

## 📞 支援

如果您遇到問題或有建議，請：

1. 查看 [常見問題](#常見問題)
2. 運行 `.\test-system.ps1` 進行診斷
3. 查看 GitHub Issues
4. 聯繫開發團隊

---

**🎉 享受多 LLM 大亂鬥的樂趣！**
