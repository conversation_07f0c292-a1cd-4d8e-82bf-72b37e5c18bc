@echo off
chcp 65001 >nul
title Browser MCP 多 LLM 系統啟動器

echo.
echo ========================================
echo   🚀 Browser MCP 多 LLM 系統啟動器
echo ========================================
echo.

echo 🔍 檢查 PowerShell...
powershell -Command "if ($PSVersionTable.PSVersion.Major -lt 5) { exit 1 }" >nul 2>&1
if errorlevel 1 (
    echo ❌ 需要 PowerShell 5.1 或更高版本
    echo 請安裝最新版本的 PowerShell
    pause
    exit /b 1
)
echo ✅ PowerShell 版本正常

echo.
echo 🎯 選擇啟動模式:
echo   1. 離線模式 (推薦，無需 API Key)
echo   2. 雲端模式 (需要 OPERATIVE_API_KEY)
echo   3. 環境檢查
echo   4. 系統測試
echo.

set /p choice="請選擇 (1-4): "

if "%choice%"=="1" goto local_mode
if "%choice%"=="2" goto cloud_mode
if "%choice%"=="3" goto check_env
if "%choice%"=="4" goto test_system

echo ❌ 無效選擇，使用預設離線模式
goto local_mode

:local_mode
echo.
echo 🚀 啟動離線模式...
echo.
powershell -ExecutionPolicy Bypass -File "start-system.ps1" -Local
goto end

:cloud_mode
echo.
set /p api_key="請輸入您的 OPERATIVE_API_KEY: "
if "%api_key%"=="" (
    echo ❌ API Key 不能為空
    pause
    exit /b 1
)
echo.
echo 🚀 啟動雲端模式...
echo.
powershell -ExecutionPolicy Bypass -File "start-system.ps1" -Cloud -ApiKey "%api_key%"
goto end

:check_env
echo.
echo 🔍 執行環境檢查...
echo.
powershell -ExecutionPolicy Bypass -File "check-setup.ps1" -Verbose
pause
goto menu

:test_system
echo.
echo 🧪 執行系統測試...
echo.
powershell -ExecutionPolicy Bypass -File "test-system.ps1"
pause
goto menu

:menu
echo.
echo 是否要返回主選單? (y/n)
set /p return_menu=""
if /i "%return_menu%"=="y" goto start
if /i "%return_menu%"=="yes" goto start
goto end

:start
cls
goto :eof

:end
echo.
echo 🎉 操作完成！
echo.
echo 💡 提示:
echo   - 系統啟動後會自動打開瀏覽器
echo   - 首次使用需要登入各 LLM 服務
echo   - 如有問題請執行環境檢查
echo.
pause
