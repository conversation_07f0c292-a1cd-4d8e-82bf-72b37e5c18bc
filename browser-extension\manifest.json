{"manifest_version": 3, "name": "Browser MCP Multi-LLM Controller", "version": "1.0.0", "description": "控制多個AI網站進行同步對話的瀏覽器擴充功能", "permissions": ["activeTab", "tabs", "storage", "scripting"], "host_permissions": ["https://chat.openai.com/*", "https://claude.ai/*", "https://gemini.google.com/*", "https://grok.x.ai/*", "http://localhost:*/*", "file://*/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["https://chat.openai.com/*"], "js": ["content-scripts/chatgpt.js"], "run_at": "document_end"}, {"matches": ["https://claude.ai/*"], "js": ["content-scripts/claude.js"], "run_at": "document_end"}, {"matches": ["https://gemini.google.com/*"], "js": ["content-scripts/gemini.js"], "run_at": "document_end"}, {"matches": ["https://grok.x.ai/*"], "js": ["content-scripts/grok.js"], "run_at": "document_end"}], "web_accessible_resources": [{"resources": ["injected-scripts/*.js"], "matches": ["https://chat.openai.com/*", "https://claude.ai/*", "https://gemini.google.com/*", "https://grok.x.ai/*"]}], "action": {"default_popup": "popup.html", "default_title": "Browser MCP Multi-LLM"}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}