# Browser MCP Multi-LLM Controller

這是一個Chrome擴充功能，可以同時控制多個AI網站（ChatGPT、<PERSON>、Gemini、<PERSON><PERSON>）進行對話。

## 功能特色

- 🚀 **一次輸入，多重回應**：在forum.html輸入一次prompt，同時獲得四個AI的回應
- 🔄 **即時串流**：支援即時顯示AI回應內容
- 🎯 **智能分頁管理**：自動管理AI網站分頁，無需手動切換
- 📊 **統一格式顯示**：將所有AI回應以一致格式呈現
- ⚡ **防反機器人偵測**：內建速率限制，避免觸發反機器人機制

## 安裝步驟

### 1. 載入擴充功能

1. 開啟Chrome瀏覽器
2. 前往 `chrome://extensions/`
3. 開啟右上角的「開發人員模式」
4. 點擊「載入未封裝項目」
5. 選擇 `browser-extension` 資料夾
6. 確認擴充功能已成功載入

### 2. 登入AI網站

在使用前，請先登入以下AI網站：

- [ChatGPT](https://chat.openai.com) - OpenAI帳號
- [<PERSON>](https://claude.ai) - Anthropic帳號  
- [Gemini](https://gemini.google.com) - Google帳號
- [Grok](https://grok.x.ai) - X (Twitter) 帳號

### 3. 開啟控制面板

1. 點擊瀏覽器工具列中的Browser MCP圖標
2. 點擊「開啟控制面板」按鈕
3. 或直接開啟 `frontend/forum.html` 文件

## 使用方法

1. **選擇AI模型**：勾選想要使用的AI模型
2. **輸入問題**：在文字框中輸入您的問題
3. **發送請求**：點擊「發送到所有選中的 LLM」按鈕
4. **查看回應**：等待各個AI的回應顯示在下方

## 技術架構

```
browser-extension/
├── manifest.json          # 擴充功能配置
├── background.js          # 背景腳本（核心控制邏輯）
├── popup.html/js          # 擴充功能彈出視窗
├── content-scripts/       # 內容腳本
│   ├── chatgpt.js        # ChatGPT網站控制
│   ├── claude.js         # Claude網站控制
│   ├── gemini.js         # Gemini網站控制
│   └── grok.js           # Grok網站控制
└── icons/                # 擴充功能圖標

frontend/
├── forum.html            # 主控制面板
├── app.js               # 前端邏輯（已更新支援擴充功能通訊）
└── styles.css           # 樣式文件
```

## 工作原理

1. **擴充功能架構**：使用Chrome Extension Manifest V3
2. **跨分頁通訊**：透過background script協調多個分頁
3. **DOM操作**：content scripts注入到各AI網站進行DOM操作
4. **即時監聽**：使用MutationObserver監聽AI回應生成
5. **訊息傳遞**：forum.html透過chrome.runtime API與擴充功能通訊

## 注意事項

- ⚠️ 請確保已登入所有要使用的AI網站
- ⚠️ 某些AI網站可能有使用頻率限制
- ⚠️ 網站UI更新可能影響功能，需要更新DOM選擇器
- ⚠️ 建議在使用前先測試各個AI網站的可用性

## 故障排除

### 擴充功能無法載入
- 檢查是否開啟開發人員模式
- 確認manifest.json格式正確
- 查看Chrome擴充功能頁面的錯誤訊息

### AI網站無回應
- 確認已登入對應的AI網站
- 檢查網站是否正常運作
- 查看瀏覽器開發者工具的Console錯誤

### 回應顯示異常
- 重新整理forum.html頁面
- 檢查擴充功能是否正常運作
- 確認網站DOM結構未改變

## 開發說明

如需修改或擴展功能：

1. **修改DOM選擇器**：在對應的content script中更新選擇器
2. **新增AI網站**：創建新的content script並更新manifest.json
3. **調整UI**：修改forum.html和styles.css
4. **除錯**：使用Chrome開發者工具查看Console輸出

## 版本歷史

- v1.0.0 - 初始版本，支援四大AI平台同步對話

## 授權

此專案僅供學習和研究使用。請遵守各AI平台的使用條款。
