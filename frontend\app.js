// Browser MCP Multi-LLM Forum Application

class MultiLLMApp {
    constructor() {
        this.apiBaseUrl = window.location.origin;
        this.websocket = null;
        this.currentResults = null;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.checkServerStatus();
        this.initWebSocket();
        this.loadBrowserTabs();
    }
    
    bindEvents() {
        // Main buttons
        document.getElementById('send-button').addEventListener('click', () => this.sendPrompt());
        document.getElementById('clear-button').addEventListener('click', () => this.clearForm());
        document.getElementById('export-button').addEventListener('click', () => this.exportResults());
        document.getElementById('compare-button').addEventListener('click', () => this.showCompareModal());
        document.getElementById('refresh-tabs').addEventListener('click', () => this.loadBrowserTabs());
        
        // Modal events
        const modal = document.getElementById('compare-modal');
        const closeBtn = modal.querySelector('.modal-close');
        closeBtn.addEventListener('click', () => this.hideCompareModal());
        
        // Close modal when clicking outside
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.hideCompareModal();
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                this.sendPrompt();
            }
            if (e.key === 'Escape') {
                this.hideCompareModal();
            }
        });
        
        // Auto-resize textarea
        const promptInput = document.getElementById('prompt-input');
        promptInput.addEventListener('input', () => {
            promptInput.style.height = 'auto';
            promptInput.style.height = promptInput.scrollHeight + 'px';
        });
    }
    
    async checkServerStatus() {
        // For standalone HTML version, show as online
        this.updateStatus('proxy-status', true, 'Proxy 服務');
        this.updateStatus('mcp-status', true, 'MCP 服務');
    }
    
    updateStatus(elementId, isOnline, serviceName) {
        const element = document.getElementById(elementId);
        element.textContent = isOnline ? '線上' : '離線';
        element.className = `status-value ${isOnline ? 'online' : 'offline'}`;
        
        if (!isOnline) {
            this.showToast(`${serviceName}離線`, 'error');
        }
    }
    
    initWebSocket() {
        // 獨立版本不需要WebSocket連接
        console.log('Standalone mode - WebSocket disabled');
    }
    
    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'pong':
                // Keep-alive response
                break;
            case 'prompt_status':
                this.updatePromptStatus(data.status, data.message);
                break;
            case 'error':
                this.showToast(data.message, 'error');
                break;
            default:
                console.log('Unknown WebSocket message:', data);
        }
    }
    
    async sendPrompt() {
        const promptInput = document.getElementById('prompt-input');
        const sendButton = document.getElementById('send-button');
        const prompt = promptInput.value.trim();
        
        if (!prompt) {
            this.showToast('請輸入 prompt', 'warning');
            promptInput.focus();
            return;
        }
        
        // Get selected models
        const selectedModels = this.getSelectedModels();
        if (selectedModels.length === 0) {
            this.showToast('請至少選擇一個 LLM 模型', 'warning');
            return;
        }
        
        // Disable send button and show loading
        sendButton.disabled = true;
        this.showLoading(true);
        this.clearResults();
        
        // 模擬AI回應 - 獨立版本
        console.log('Sending prompt to models:', selectedModels);

        const mockResponses = {
            chatgpt: `**ChatGPT 回應：**\n\n關於您的問題「${prompt}」，我認為這是一個很有趣的話題。\n\n從技術角度來看，這涉及到多個層面的考量：\n\n1. **核心概念理解**：首先需要理解問題的本質\n2. **技術分析**：分析相關的技術要點和挑戰\n3. **解決方案**：提供實用且可行的建議\n\n希望這個回答對您有幫助！如果需要更詳細的說明，請隨時告訴我。`,

            claude: `**Claude 回應：**\n\n您提出的「${prompt}」是個深思熟慮的問題。讓我從幾個角度來分析：\n\n**分析框架：**\n• 理論基礎：這個問題涉及的基本原理和概念\n• 實際應用：在現實場景中的具體運用方式\n• 潛在挑戰：可能遇到的困難和相應的解決策略\n\n**建議方向：**\n我建議採用循序漸進的方法來處理這個問題，先從基礎概念開始，逐步深入到具體的實施細節。\n\n這樣的方法既能確保理解的深度，也能保證實施的可行性。`,

            gemini: `**Gemini 回應：**\n\n針對「${prompt}」這個問題，我可以提供以下見解：\n\n🔍 **核心要點：**\n• 問題的本質和重要性分析\n• 相關技術發展趨勢和最新進展\n• 業界最佳實踐和成功案例\n\n📊 **數據支持：**\n根據最新的研究和發展趨勢，這個領域正在快速演進，有很多創新的解決方案正在湧現。\n\n💡 **創新思路：**\n我們可以考慮結合多種方法，採用混合式的解決方案來達到最佳效果。\n\n🎯 **行動建議：**\n建議從小規模試點開始，逐步擴展到更大的應用範圍。`,

            grok: `**Grok 回應：**\n\n哈！「${prompt}」這個問題問得很棒！🚀\n\n從宇宙的角度來看，這個問題其實反映了人類對知識和理解的永恆追求。讓我用一種更有趣的方式來回答：\n\n🌟 **宇宙級洞察：**\n• 這個問題比表面看起來更加深刻和有意義\n• 答案可能就隱藏在我們日常生活的細節中\n• 有時候最簡單直接的解釋往往就是最好的答案\n\n🎭 **哲學思考：**\n在這個無限的宇宙中，每個問題都有它存在的獨特意義和價值。\n\n🌌 **終極建議：**\n保持好奇心，勇於探索，答案總會在某個意想不到的時刻出現！`
        };

        // 模擬延遲
        setTimeout(() => {
            try {
                const results = {
                    success: true,
                    prompt: prompt,
                    timestamp: new Date().toISOString(),
                    results: selectedModels.map(model => ({
                        model: model,
                        status: 'success',
                        response: mockResponses[model] || `${model.toUpperCase()} 的詳細回應內容...`,
                        timestamp: new Date().toISOString(),
                        tokens: Math.floor(Math.random() * 500) + 100
                    }))
                };

                this.currentResults = results;
                this.displayResults(results);
                this.showToast('所有 LLM 回應完成！', 'success');

                // Enable export and compare buttons
                document.getElementById('export-button').disabled = false;
                document.getElementById('compare-button').disabled = false;

            } catch (error) {
                console.error('Error in mock response:', error);
                this.showToast(`處理回應時發生錯誤: ${error.message}`, 'error');
                this.displayError(error.message);
            } finally {
                sendButton.disabled = false;
                this.showLoading(false);
            }
        }, 2000);
    }
    
    getSelectedModels() {
        const checkboxes = document.querySelectorAll('.model-checkboxes input[type="checkbox"]:checked');
        return Array.from(checkboxes).map(cb => cb.value);
    }
    
    showLoading(show) {
        const loadingIndicator = document.getElementById('loading-indicator');
        loadingIndicator.style.display = show ? 'block' : 'none';
    }
    
    clearResults() {
        const resultsContainer = document.getElementById('results-container');
        resultsContainer.innerHTML = '';
        
        // Disable export and compare buttons
        document.getElementById('export-button').disabled = true;
        document.getElementById('compare-button').disabled = true;
    }
    
    displayResults(data) {
        const resultsContainer = document.getElementById('results-container');
        
        // Parse the MCP response
        let mcpResults = {};
        try {
            if (data.data && data.data.result && data.data.result.length > 0) {
                const resultText = data.data.result[0].text;
                mcpResults = this.parseMCPResults(resultText);
            }
        } catch (error) {
            console.error('Error parsing MCP results:', error);
        }
        
        // Display results for each model
        data.models.forEach(model => {
            const resultCard = this.createResultCard(model, mcpResults[model] || {});
            resultsContainer.appendChild(resultCard);
        });
    }
    
    parseMCPResults(resultText) {
        const results = {};
        const modelConfigs = {
            'chatgpt': 'ChatGPT',
            'claude': 'Claude',
            'gemini': 'Gemini',
            'grok': 'Grok'
        };
        
        // Simple parsing - this could be improved with more sophisticated parsing
        for (const [modelKey, modelName] of Object.entries(modelConfigs)) {
            const modelSection = resultText.split(`## ${modelName}`)[1];
            if (modelSection) {
                const nextSection = modelSection.split('## ')[0];
                
                if (nextSection.includes('❌ Send Error:')) {
                    results[modelKey] = {
                        status: 'error',
                        error: nextSection.split('❌ Send Error:')[1].split('\n')[0].trim()
                    };
                } else if (nextSection.includes('⚠️ Response Error:')) {
                    results[modelKey] = {
                        status: 'error',
                        error: nextSection.split('⚠️ Response Error:')[1].split('\n')[0].trim()
                    };
                } else if (nextSection.includes('✅ Response:')) {
                    results[modelKey] = {
                        status: 'success',
                        response: nextSection.split('✅ Response:')[1].split('\n')[0].trim()
                    };
                } else if (nextSection.includes('⏳ Response:')) {
                    results[modelKey] = {
                        status: 'loading',
                        message: '回應生成中...'
                    };
                }
            }
        }
        
        return results;
    }
    
    createResultCard(model, result) {
        const modelNames = {
            'chatgpt': 'ChatGPT',
            'claude': 'Claude',
            'gemini': 'Gemini',
            'grok': 'Grok'
        };
        
        const card = document.createElement('div');
        card.className = `result-card ${result.status || 'loading'}`;
        
        const statusText = {
            'success': '成功',
            'error': '錯誤',
            'loading': '處理中'
        };
        
        card.innerHTML = `
            <div class="result-header">
                <div class="result-title">${modelNames[model] || model}</div>
                <div class="result-status ${result.status || 'loading'}">
                    ${statusText[result.status] || '處理中'}
                </div>
            </div>
            <div class="result-content">
                ${this.formatResultContent(result)}
            </div>
        `;
        
        return card;
    }
    
    formatResultContent(result) {
        if (result.error) {
            return `<div style="color: var(--error-color);">❌ ${result.error}</div>`;
        } else if (result.response) {
            return `<div>${result.response}</div>`;
        } else if (result.message) {
            return `<div style="color: var(--warning-color);">⏳ ${result.message}</div>`;
        } else {
            return '<div style="color: var(--text-secondary);">等待回應中...</div>';
        }
    }
    
    displayError(errorMessage) {
        const resultsContainer = document.getElementById('results-container');
        resultsContainer.innerHTML = `
            <div class="result-card error">
                <div class="result-header">
                    <div class="result-title">系統錯誤</div>
                    <div class="result-status error">錯誤</div>
                </div>
                <div class="result-content">
                    <div style="color: var(--error-color);">❌ ${errorMessage}</div>
                </div>
            </div>
        `;
    }
    
    clearForm() {
        document.getElementById('prompt-input').value = '';
        document.getElementById('prompt-input').style.height = 'auto';
        this.clearResults();
        this.currentResults = null;
    }
    
    async loadBrowserTabs() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/api/browser-tabs`);
            const data = await response.json();
            
            if (data.success) {
                this.displayBrowserTabs(data.data);
            } else {
                throw new Error(data.message || 'Failed to load browser tabs');
            }
            
        } catch (error) {
            console.error('Error loading browser tabs:', error);
            this.displayBrowserTabsError(error.message);
        }
    }
    
    displayBrowserTabs(tabsData) {
        const tabsContainer = document.getElementById('tabs-container');
        
        if (!tabsData || !tabsData.result || tabsData.result.length === 0) {
            tabsContainer.innerHTML = '<div class="tab-card">目前沒有開啟的瀏覽器分頁</div>';
            return;
        }
        
        const tabsText = tabsData.result[0].text;
        if (tabsText.includes('No browser tabs currently open')) {
            tabsContainer.innerHTML = '<div class="tab-card">目前沒有開啟的瀏覽器分頁</div>';
            return;
        }
        
        // Parse tabs information
        const tabLines = tabsText.split('\n').filter(line => line.startsWith('Tab '));
        
        if (tabLines.length === 0) {
            tabsContainer.innerHTML = '<div class="tab-card">目前沒有開啟的瀏覽器分頁</div>';
            return;
        }
        
        tabsContainer.innerHTML = tabLines.map(line => {
            const parts = line.split(': ');
            const tabId = parts[0].replace('Tab ', '');
            const urlAndTitle = parts[1] || 'Unknown';
            const [url, title] = urlAndTitle.split(' - ');
            
            return `
                <div class="tab-card">
                    <div class="tab-title">${title || 'No title'}</div>
                    <div class="tab-url">${url || 'Unknown URL'}</div>
                </div>
            `;
        }).join('');
    }
    
    displayBrowserTabsError(errorMessage) {
        const tabsContainer = document.getElementById('tabs-container');
        tabsContainer.innerHTML = `
            <div class="tab-card" style="border-color: var(--error-color);">
                <div class="tab-title" style="color: var(--error-color);">載入錯誤</div>
                <div class="tab-url">${errorMessage}</div>
            </div>
        `;
    }
    
    exportResults() {
        if (!this.currentResults) {
            this.showToast('沒有可匯出的結果', 'warning');
            return;
        }
        
        const exportData = {
            timestamp: new Date().toISOString(),
            prompt: document.getElementById('prompt-input').value,
            models: this.currentResults.models,
            results: this.currentResults.data
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `llm-results-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showToast('結果已匯出', 'success');
    }
    
    showCompareModal() {
        if (!this.currentResults) {
            this.showToast('沒有可比較的結果', 'warning');
            return;
        }
        
        const modal = document.getElementById('compare-modal');
        const compareContent = document.getElementById('compare-content');
        
        // Generate comparison content
        compareContent.innerHTML = this.generateComparisonContent();
        
        modal.style.display = 'flex';
    }
    
    hideCompareModal() {
        const modal = document.getElementById('compare-modal');
        modal.style.display = 'none';
    }
    
    generateComparisonContent() {
        // This is a simplified comparison - could be enhanced with more sophisticated analysis
        const prompt = document.getElementById('prompt-input').value;
        
        return `
            <div style="margin-bottom: 20px;">
                <h4>📝 原始 Prompt</h4>
                <div style="background-color: var(--background-color); padding: 15px; border-radius: var(--border-radius); margin-top: 10px;">
                    ${prompt}
                </div>
            </div>
            
            <div>
                <h4>📊 回應比較</h4>
                <p style="color: var(--text-secondary); margin-top: 10px;">
                    詳細的比較分析功能正在開發中。目前您可以在主頁面查看各個 LLM 的回應結果。
                </p>
                <p style="color: var(--text-secondary);">
                    未來版本將包含：
                </p>
                <ul style="color: var(--text-secondary); margin-left: 20px;">
                    <li>回應品質評分</li>
                    <li>相似度分析</li>
                    <li>關鍵詞提取</li>
                    <li>情感分析</li>
                    <li>回應長度統計</li>
                </ul>
            </div>
        `;
    }
    
    showToast(message, type = 'info') {
        const toastContainer = document.getElementById('toast-container');
        
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div style="font-weight: 600; margin-bottom: 5px;">
                ${type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'}
                ${type === 'success' ? '成功' : type === 'error' ? '錯誤' : type === 'warning' ? '警告' : '資訊'}
            </div>
            <div>${message}</div>
        `;
        
        toastContainer.appendChild(toast);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MultiLLMApp();
});
