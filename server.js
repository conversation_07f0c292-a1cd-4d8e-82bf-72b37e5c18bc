import express from 'express';        // 若保留 CommonJS 就改回 require()
const app = express();
const PORT = 3000;                    // 別跟 BrowserMCP 的 3333 撞
app.use(express.json());

app.post('/api', async (req, res) => {
  const data = await fetch('http://localhost:3333/browser', {
    method: 'POST',
    headers: {'Content-Type':'application/json'},
    body: JSON.stringify(req.body)
  }).then(r => r.json()).catch(e => console.error(e));
  res.json(data);
});

app.listen(PORT, () => console.log(`listening ${PORT}`));