@echo off
title OneClick Start - Browser MCP Multi-LLM System

echo.
echo ========================================
echo   OneClick Start - Browser MCP Multi-LLM System
echo ========================================
echo.

echo Checking and installing required tools...

REM Check Chocolatey
choco --version >nul 2>&1
if errorlevel 1 (
    echo Installing Chocolatey package manager...
    powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))"
    if errorlevel 1 (
        echo Chocolatey install failed, trying manual install...
        goto manual_install
    )
    echo Chocolatey installed successfully
    call refreshenv
)

REM Check and install Python
python --version >nul 2>&1
if errorlevel 1 (
    echo Installing Python...
    choco install python -y
    call refreshenv
)

REM Check and install Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo Installing Node.js...
    choco install nodejs -y
    call refreshenv
)

REM Check and install Git
git --version >nul 2>&1
if errorlevel 1 (
    echo Installing Git...
    choco install git -y
    call refreshenv
)

REM Install uv
pip install uv >nul 2>&1
if errorlevel 1 (
    echo Installing uv...
    python -m pip install uv
)

echo All tools installed successfully!

goto start_system

:manual_install
echo.
echo Auto install failed, please manually install:
echo   1. Python: https://www.python.org/downloads/
echo   2. Node.js: https://nodejs.org/
echo   3. Check "Add to PATH" during installation
echo.
echo Run this script again after installation
pause
exit /b 1

:start_system
echo.
echo Starting system...
echo.

REM Install Python dependencies
echo Installing Python dependencies...
cd web-eval-agent
uv pip install -e . >nul 2>&1
if errorlevel 1 (
    pip install -e . >nul 2>&1
)

REM Install Playwright
echo Installing Playwright...
uv run playwright install chromium >nul 2>&1
if errorlevel 1 (
    python -m playwright install chromium >nul 2>&1
)

cd ..

REM Install Node.js dependencies
echo Installing Node.js dependencies...
cd prompt-proxy
npm install >nul 2>&1
cd ..

REM Start MCP server
echo Starting MCP server...
cd web-eval-agent
start /B uv run python -m webEvalAgent.mcp_server --local --host 0.0.0.0 --port 5000
cd ..

REM Wait for MCP server
echo Waiting for MCP server to start...
timeout /t 5 /nobreak >nul

REM Start Proxy server
echo Starting Proxy server...
cd prompt-proxy
start /B node server.js
cd ..

REM Wait for Proxy server
echo Waiting for Proxy server to start...
timeout /t 3 /nobreak >nul

REM Open browser
echo Opening browser...
start http://localhost:3000

echo.
echo ========================================
echo   System Started Successfully!
echo ========================================
echo.
echo Service Status:
echo   - MCP Server: http://localhost:5000
echo   - Proxy Server: http://localhost:3000
echo   - Frontend: http://localhost:3000
echo.
echo Usage:
echo   - Browser opened automatically
echo   - Select LLM models to use
echo   - Enter prompt and send to multiple AIs
echo   - Close this window to stop services
echo.
echo Start your Multi-LLM battle!
echo.

pause
