# Browser MCP 多 LLM 系統啟動腳本
# 使用方式: .\start-system.ps1

param(
    [switch]$Local,
    [switch]$Cloud,
    [string]$ApiKey = "",
    [int]$McpPort = 5000,
    [int]$ProxyPort = 3000,
    [switch]$Help
)

if ($Help) {
    Write-Host "Browser MCP 多 LLM 系統啟動腳本" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "使用方式:" -ForegroundColor Yellow
    Write-Host "  .\start-system.ps1 -Local                    # 離線模式啟動"
    Write-Host "  .\start-system.ps1 -Cloud -ApiKey 'your-key' # 雲端模式啟動"
    Write-Host "  .\start-system.ps1 -Help                     # 顯示幫助"
    Write-Host ""
    Write-Host "參數說明:" -ForegroundColor Yellow
    Write-Host "  -Local       使用離線模式（不需要 API Key）"
    Write-Host "  -Cloud       使用雲端模式（需要 API Key）"
    Write-Host "  -ApiKey      OperativeAI API Key（雲端模式必需）"
    Write-Host "  -McpPort     MCP 服務器埠號（預設: 5000）"
    Write-Host "  -ProxyPort   Proxy 服務器埠號（預設: 3000）"
    Write-Host "  -Help        顯示此幫助訊息"
    exit 0
}

Write-Host "🚀 Browser MCP 多 LLM 系統啟動中..." -ForegroundColor Cyan
Write-Host "=" * 50

# 檢查參數
if (-not $Local -and -not $Cloud) {
    Write-Host "❌ 錯誤: 請指定 -Local 或 -Cloud 模式" -ForegroundColor Red
    Write-Host "使用 -Help 查看詳細說明"
    exit 1
}

if ($Cloud -and -not $ApiKey) {
    Write-Host "❌ 錯誤: 雲端模式需要提供 -ApiKey 參數" -ForegroundColor Red
    exit 1
}

# 檢查必要工具
Write-Host "`n🔍 檢查系統需求..." -ForegroundColor Yellow

# 檢查 Python
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python 未安裝或不在 PATH 中" -ForegroundColor Red
    exit 1
}

# 檢查 uv
try {
    $uvVersion = uv --version 2>&1
    Write-Host "✅ uv: $uvVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ uv 未安裝，請執行: irm https://astral.sh/uv/install.ps1 | iex" -ForegroundColor Red
    exit 1
}

# 檢查 Node.js
try {
    $nodeVersion = node --version 2>&1
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js 未安裝，請從 https://nodejs.org 下載安裝" -ForegroundColor Red
    exit 1
}

# 檢查專案結構
Write-Host "`n🔍 檢查專案結構..." -ForegroundColor Yellow

$requiredPaths = @(
    "web-eval-agent/webEvalAgent/mcp_server.py",
    "prompt-proxy/package.json",
    "frontend/forum.html"
)

foreach ($path in $requiredPaths) {
    if (Test-Path $path) {
        Write-Host "✅ $path" -ForegroundColor Green
    } else {
        Write-Host "❌ $path 不存在" -ForegroundColor Red
        exit 1
    }
}

# 檢查埠號占用
Write-Host "`n🔍 檢查埠號占用..." -ForegroundColor Yellow

function Test-Port {
    param([int]$Port)
    try {
        $result = netstat -ano | findstr ":$Port"
        return $result.Length -gt 0
    } catch {
        return $false
    }
}

if (Test-Port $McpPort) {
    Write-Host "⚠️  埠號 $McpPort 被占用，嘗試終止相關程序..." -ForegroundColor Yellow
    try {
        $processes = netstat -ano | findstr ":$McpPort"
        foreach ($line in $processes) {
            $pid = ($line -split '\s+')[-1]
            if ($pid -match '^\d+$') {
                taskkill /PID $pid /F 2>$null
            }
        }
        Start-Sleep -Seconds 2
    } catch {
        Write-Host "⚠️  無法自動終止程序，請手動處理" -ForegroundColor Yellow
    }
}

if (Test-Port $ProxyPort) {
    Write-Host "⚠️  埠號 $ProxyPort 被占用，嘗試終止相關程序..." -ForegroundColor Yellow
    try {
        $processes = netstat -ano | findstr ":$ProxyPort"
        foreach ($line in $processes) {
            $pid = ($line -split '\s+')[-1]
            if ($pid -match '^\d+$') {
                taskkill /PID $pid /F 2>$null
            }
        }
        Start-Sleep -Seconds 2
    } catch {
        Write-Host "⚠️  無法自動終止程序，請手動處理" -ForegroundColor Yellow
    }
}

# 設定環境變數
if ($Cloud) {
    $env:OPERATIVE_API_KEY = $ApiKey
    Write-Host "✅ 已設定 API Key" -ForegroundColor Green
}

$env:MCP_SERVER_URL = "http://localhost:$McpPort"

# 安裝依賴
Write-Host "`n📦 安裝依賴..." -ForegroundColor Yellow

# 安裝 Python 依賴
Write-Host "安裝 Python 依賴..."
Set-Location "web-eval-agent"
try {
    uv pip install -e . 2>&1 | Out-Null
    Write-Host "✅ Python 依賴安裝完成" -ForegroundColor Green
} catch {
    Write-Host "❌ Python 依賴安裝失敗" -ForegroundColor Red
    exit 1
}
Set-Location ".."

# 安裝 Node.js 依賴
Write-Host "安裝 Node.js 依賴..."
Set-Location "prompt-proxy"
try {
    npm install 2>&1 | Out-Null
    Write-Host "✅ Node.js 依賴安裝完成" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js 依賴安裝失敗" -ForegroundColor Red
    exit 1
}
Set-Location ".."

# 啟動服務
Write-Host "`n🚀 啟動服務..." -ForegroundColor Yellow

# 啟動 MCP 服務器
Write-Host "啟動 MCP 服務器 (埠號: $McpPort)..."
Set-Location "web-eval-agent"

$mcpArgs = @(
    "run", "python", "-m", "webEvalAgent.mcp_server",
    "--transport", "http",
    "--host", "0.0.0.0",
    "--port", $McpPort.ToString()
)

if ($Local) {
    $mcpArgs += "--local"
}

$mcpProcess = Start-Process -FilePath "uv" -ArgumentList $mcpArgs -NoNewWindow -PassThru
Set-Location ".."

# 等待 MCP 服務器啟動
Write-Host "等待 MCP 服務器啟動..."
$maxWait = 30
$waited = 0
do {
    Start-Sleep -Seconds 1
    $waited++
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$McpPort/health" -TimeoutSec 2 -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ MCP 服務器啟動成功" -ForegroundColor Green
            break
        }
    } catch {
        # 繼續等待
    }
    
    if ($waited -ge $maxWait) {
        Write-Host "❌ MCP 服務器啟動超時" -ForegroundColor Red
        $mcpProcess.Kill()
        exit 1
    }
} while ($true)

# 啟動 Proxy 服務器
Write-Host "啟動 Proxy 服務器 (埠號: $ProxyPort)..."
Set-Location "prompt-proxy"

$proxyProcess = Start-Process -FilePath "npm" -ArgumentList @("start") -NoNewWindow -PassThru
Set-Location ".."

# 等待 Proxy 服務器啟動
Write-Host "等待 Proxy 服務器啟動..."
$maxWait = 15
$waited = 0
do {
    Start-Sleep -Seconds 1
    $waited++
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$ProxyPort/health" -TimeoutSec 2 -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Proxy 服務器啟動成功" -ForegroundColor Green
            break
        }
    } catch {
        # 繼續等待
    }
    
    if ($waited -ge $maxWait) {
        Write-Host "❌ Proxy 服務器啟動超時" -ForegroundColor Red
        $mcpProcess.Kill()
        $proxyProcess.Kill()
        exit 1
    }
} while ($true)

# 顯示啟動完成資訊
Write-Host "`n" + "=" * 50
Write-Host "🎉 系統啟動完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📊 服務狀態:" -ForegroundColor Cyan
Write-Host "  🔧 MCP 服務器:    http://localhost:$McpPort" -ForegroundColor White
Write-Host "  🌐 Proxy 服務器:  http://localhost:$ProxyPort" -ForegroundColor White
Write-Host "  📱 前端介面:      http://localhost:$ProxyPort" -ForegroundColor White
Write-Host ""
Write-Host "🎯 使用方式:" -ForegroundColor Cyan
Write-Host "  1. 開啟瀏覽器訪問: http://localhost:$ProxyPort" -ForegroundColor White
Write-Host "  2. 輸入您的 prompt" -ForegroundColor White
Write-Host "  3. 選擇要使用的 LLM 模型" -ForegroundColor White
Write-Host "  4. 點擊「發送到所有 LLM」按鈕" -ForegroundColor White
Write-Host "  5. 等待並查看各個 LLM 的回應" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  注意事項:" -ForegroundColor Yellow
Write-Host "  - 首次使用時，系統會自動開啟各 LLM 網站分頁" -ForegroundColor White
Write-Host "  - 某些 LLM 可能需要手動登入" -ForegroundColor White
Write-Host "  - 按 Ctrl+C 停止所有服務" -ForegroundColor White
Write-Host ""

# 自動開啟瀏覽器
try {
    Start-Process "http://localhost:$ProxyPort"
    Write-Host "🌐 已自動開啟瀏覽器" -ForegroundColor Green
} catch {
    Write-Host "⚠️  無法自動開啟瀏覽器，請手動訪問 http://localhost:$ProxyPort" -ForegroundColor Yellow
}

# 等待用戶中斷
Write-Host "`n按 Ctrl+C 停止所有服務..." -ForegroundColor Gray

try {
    while ($true) {
        Start-Sleep -Seconds 1
    }
} finally {
    Write-Host "`n🛑 正在停止服務..." -ForegroundColor Yellow
    
    try {
        $mcpProcess.Kill()
        Write-Host "✅ MCP 服務器已停止" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  MCP 服務器停止失敗" -ForegroundColor Yellow
    }
    
    try {
        $proxyProcess.Kill()
        Write-Host "✅ Proxy 服務器已停止" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Proxy 服務器停止失敗" -ForegroundColor Yellow
    }
    
    Write-Host "👋 系統已關閉" -ForegroundColor Cyan
}
