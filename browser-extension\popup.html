<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 5px;
        }
        
        .subtitle {
            font-size: 12px;
            color: #6b7280;
        }
        
        .status-section {
            margin-bottom: 20px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            font-size: 14px;
            color: #374151;
        }
        
        .status-indicator {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-online {
            background-color: #dcfce7;
            color: #166534;
        }
        
        .status-offline {
            background-color: #fee2e2;
            color: #991b1b;
        }
        
        .actions {
            margin-top: 20px;
        }
        
        .btn {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            margin-bottom: 8px;
            transition: background-color 0.2s;
        }
        
        .btn-primary {
            background-color: #2563eb;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #1d4ed8;
        }
        
        .btn-secondary {
            background-color: #f3f4f6;
            color: #374151;
        }
        
        .btn-secondary:hover {
            background-color: #e5e7eb;
        }
        
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 11px;
            color: #9ca3af;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">Browser MCP</div>
        <div class="subtitle">多LLM同步控制</div>
    </div>
    
    <div class="status-section">
        <div class="status-item">
            <span class="status-label">ChatGPT</span>
            <span id="chatgpt-status" class="status-indicator status-offline">離線</span>
        </div>
        <div class="status-item">
            <span class="status-label">Claude</span>
            <span id="claude-status" class="status-indicator status-offline">離線</span>
        </div>
        <div class="status-item">
            <span class="status-label">Gemini</span>
            <span id="gemini-status" class="status-indicator status-offline">離線</span>
        </div>
        <div class="status-item">
            <span class="status-label">Grok</span>
            <span id="grok-status" class="status-indicator status-offline">離線</span>
        </div>
    </div>
    
    <div class="actions">
        <button id="open-forum" class="btn btn-primary">開啟控制面板</button>
        <button id="refresh-status" class="btn btn-secondary">重新整理狀態</button>
    </div>
    
    <div class="footer">
        Browser MCP v1.0.0
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
