/* Browser MCP Multi-LLM Forum Styles */

:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --border-color: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --border-radius: 8px;
    --transition: all 0.2s ease-in-out;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 10px;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 15px;
    padding: 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
}

.header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 10px;
}

.status-bar {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 10px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-label {
    font-weight: 500;
}

.status-value {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    background-color: rgba(255, 255, 255, 0.2);
}

.status-value.online {
    background-color: var(--success-color);
}

.status-value.offline {
    background-color: var(--error-color);
}

/* Input Section */
.input-section {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.prompt-container {
    max-width: 800px;
    margin: 0 auto;
}

.prompt-label {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.prompt-input {
    width: 100%;
    padding: 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    transition: var(--transition);
    background-color: var(--surface-color);
}

.prompt-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.model-selection {
    margin: 25px 0;
}

.model-label {
    display: block;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-primary);
}

.model-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 10px 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
    background-color: var(--surface-color);
}

.checkbox-label:hover {
    border-color: var(--primary-color);
    background-color: rgba(37, 99, 235, 0.05);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: var(--transition);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 12px;
}

.button-group {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
}

.send-button, .clear-button, .export-button, .compare-button, .refresh-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
}

.send-button {
    background-color: var(--primary-color);
    color: white;
}

.send-button:hover:not(:disabled) {
    background-color: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.send-button:disabled {
    background-color: var(--secondary-color);
    cursor: not-allowed;
    opacity: 0.6;
}

.clear-button {
    background-color: var(--secondary-color);
    color: white;
}

.clear-button:hover {
    background-color: #475569;
}

.export-button, .compare-button {
    background-color: var(--success-color);
    color: white;
}

.export-button:hover:not(:disabled), .compare-button:hover:not(:disabled) {
    background-color: #059669;
}

.export-button:disabled, .compare-button:disabled {
    background-color: var(--secondary-color);
    cursor: not-allowed;
    opacity: 0.6;
}

.refresh-button {
    background-color: var(--warning-color);
    color: white;
}

.refresh-button:hover {
    background-color: #d97706;
}

/* Results Section */
.results-section {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.results-header h2 {
    color: var(--text-primary);
    font-size: 1.5rem;
}

.results-controls {
    display: flex;
    gap: 10px;
}

.loading-indicator {
    text-align: center;
    padding: 40px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
    margin-top: 20px;
}

.progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    width: 0%;
    animation: progress 3s ease-in-out infinite;
}

@keyframes progress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

.results-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.result-card {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    background-color: var(--surface-color);
    transition: var(--transition);
}

.result-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.result-card.success {
    border-color: var(--success-color);
}

.result-card.error {
    border-color: var(--error-color);
}

.result-card.loading {
    border-color: var(--warning-color);
}

.result-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.result-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.result-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.result-status.success {
    background-color: var(--success-color);
    color: white;
}

.result-status.error {
    background-color: var(--error-color);
    color: white;
}

.result-status.loading {
    background-color: var(--warning-color);
    color: white;
}

.result-content {
    color: var(--text-secondary);
    line-height: 1.6;
    max-height: 300px;
    overflow-y: auto;
}

.result-content pre {
    background-color: var(--background-color);
    padding: 15px;
    border-radius: var(--border-radius);
    overflow-x: auto;
    font-size: 0.9rem;
}

/* Browser Tabs Section */
.tabs-section {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.tabs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.tabs-header h3 {
    color: var(--text-primary);
}

.tabs-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.tab-card {
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--background-color);
}

.tab-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-primary);
}

.tab-url {
    font-size: 0.9rem;
    color: var(--text-secondary);
    word-break: break-all;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.toast {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px 20px;
    margin-bottom: 10px;
    box-shadow: var(--shadow-lg);
    animation: slideIn 0.3s ease-out;
    max-width: 400px;
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--error-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: var(--surface-color);
    border-radius: var(--border-radius);
    max-width: 90%;
    max-height: 90%;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .status-bar {
        flex-direction: column;
        gap: 10px;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .results-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .results-controls {
        width: 100%;
        justify-content: flex-start;
    }
    
    .results-container {
        grid-template-columns: 1fr;
    }
    
    .tabs-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .tabs-container {
        grid-template-columns: 1fr;
    }
}
