// Browser MCP Multi-LLM Forum Application

class MultiLLMApp {
    constructor() {
        this.extensionPort = null;
        this.currentResults = null;
        this.isExtensionAvailable = false;

        this.init();
    }
    
    init() {
        this.bindEvents();
        this.connectToExtension();
        this.loadBrowserTabs();
    }
    
    bindEvents() {
        // Main buttons
        document.getElementById('send-button').addEventListener('click', () => this.sendPrompt());
        document.getElementById('clear-button').addEventListener('click', () => this.clearForm());
        document.getElementById('export-button').addEventListener('click', () => this.exportResults());
        document.getElementById('compare-button').addEventListener('click', () => this.showCompareModal());
        document.getElementById('refresh-tabs').addEventListener('click', () => this.loadBrowserTabs());
        
        // Modal events
        const modal = document.getElementById('compare-modal');
        const closeBtn = modal.querySelector('.modal-close');
        closeBtn.addEventListener('click', () => this.hideCompareModal());
        
        // Close modal when clicking outside
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.hideCompareModal();
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                this.sendPrompt();
            }
            if (e.key === 'Escape') {
                this.hideCompareModal();
            }
        });
        
        // Auto-resize textarea
        const promptInput = document.getElementById('prompt-input');
        promptInput.addEventListener('input', () => {
            promptInput.style.height = 'auto';
            promptInput.style.height = promptInput.scrollHeight + 'px';
        });
    }
    
    connectToExtension() {
        // 檢查是否在Chrome擴充功能環境中
        if (typeof chrome === 'undefined' || !chrome.runtime) {
            console.log('Chrome extension API not available');
            this.isExtensionAvailable = false;
            this.updateStatus('proxy-status', false, '擴充功能');
            this.updateStatus('mcp-status', false, '瀏覽器控制');
            this.showToast('請在Chrome瀏覽器中安裝並啟用Browser MCP擴充功能', 'error');
            return;
        }

        try {
            // 嘗試連接到擴充功能
            this.extensionPort = chrome.runtime.connect({ name: 'forum-connection' });

            this.extensionPort.onMessage.addListener((message) => {
                this.handleExtensionMessage(message);
            });

            this.extensionPort.onDisconnect.addListener(() => {
                console.log('Extension disconnected');
                this.isExtensionAvailable = false;
                this.updateStatus('proxy-status', false, '擴充功能');
                this.updateStatus('mcp-status', false, '瀏覽器控制');

                // 檢查是否是因為擴充功能未載入
                if (chrome.runtime.lastError) {
                    this.showToast('擴充功能連接中斷：' + chrome.runtime.lastError.message, 'error');
                }
            });

            this.isExtensionAvailable = true;
            this.updateStatus('proxy-status', true, '擴充功能');
            this.updateStatus('mcp-status', true, '瀏覽器控制');

        } catch (error) {
            console.error('Failed to connect to extension:', error);
            this.isExtensionAvailable = false;
            this.updateStatus('proxy-status', false, '擴充功能');
            this.updateStatus('mcp-status', false, '瀏覽器控制');
            this.showToast('請安裝並啟用Browser MCP擴充功能', 'error');
        }
    }
    
    updateStatus(elementId, isOnline, serviceName) {
        const element = document.getElementById(elementId);
        element.textContent = isOnline ? '線上' : '離線';
        element.className = `status-value ${isOnline ? 'online' : 'offline'}`;
        
        if (!isOnline) {
            this.showToast(`${serviceName}離線`, 'error');
        }
    }
    
    handleExtensionMessage(message) {
        console.log('Received from extension:', message);

        switch (message.type) {
            case 'PROMPT_STARTED':
                this.handlePromptStarted(message.data);
                break;

            case 'RESPONSE_CHUNK':
                this.handleResponseChunk(message.data);
                break;

            case 'RESPONSE_COMPLETE':
                this.handleResponseComplete(message.data);
                break;

            case 'ERROR':
                this.handleError(message.data);
                break;

            case 'TAB_STATUS':
                this.updateTabStatus(message.data);
                break;
        }
    }
    
    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'pong':
                // Keep-alive response
                break;
            case 'prompt_status':
                this.updatePromptStatus(data.status, data.message);
                break;
            case 'error':
                this.showToast(data.message, 'error');
                break;
            default:
                console.log('Unknown WebSocket message:', data);
        }
    }
    
    async sendPrompt() {
        const promptInput = document.getElementById('prompt-input');
        const sendButton = document.getElementById('send-button');
        const prompt = promptInput.value.trim();
        
        if (!prompt) {
            this.showToast('請輸入 prompt', 'warning');
            promptInput.focus();
            return;
        }
        
        // Get selected models
        const selectedModels = this.getSelectedModels();
        if (selectedModels.length === 0) {
            this.showToast('請至少選擇一個 LLM 模型', 'warning');
            return;
        }
        
        // Disable send button and show loading
        sendButton.disabled = true;
        this.showLoading(true);
        this.clearResults();
        
        // 使用擴充功能發送prompt
        if (!this.isExtensionAvailable || !this.extensionPort) {
            this.showToast('擴充功能未連接，請安裝並啟用Browser MCP擴充功能', 'error');
            sendButton.disabled = false;
            this.showLoading(false);
            return;
        }

        try {
            console.log('Sending prompt to extension:', selectedModels);

            // 發送prompt到擴充功能
            this.extensionPort.postMessage({
                type: 'SEND_PROMPT',
                prompt: prompt,
                models: selectedModels
            });

        } catch (error) {
            console.error('Error sending prompt to extension:', error);
            this.showToast(`發送失敗: ${error.message}`, 'error');
            sendButton.disabled = false;
            this.showLoading(false);
        }
    }

    handlePromptStarted(data) {
        console.log('Prompt started:', data);
        this.currentResults = {
            success: true,
            prompt: data.prompt,
            timestamp: new Date().toISOString(),
            results: data.models.map(model => ({
                model: model,
                status: 'processing',
                response: '',
                timestamp: new Date().toISOString(),
                tokens: 0
            }))
        };

        this.displayResults(this.currentResults);
    }

    handleResponseChunk(data) {
        console.log('Response chunk:', data);

        if (this.currentResults) {
            const modelResult = this.currentResults.results.find(r => r.model === data.model);
            if (modelResult) {
                modelResult.response = data.fullResponse;
                modelResult.status = 'streaming';

                // 更新顯示
                this.updateModelResponse(data.model, data.fullResponse, 'streaming');
            }
        }
    }

    handleResponseComplete(data) {
        console.log('Response complete:', data);

        if (this.currentResults) {
            const modelResult = this.currentResults.results.find(r => r.model === data.model);
            if (modelResult) {
                modelResult.response = data.response;
                modelResult.status = 'success';
                modelResult.tokens = data.response.length; // 簡單的token估算

                // 更新顯示
                this.updateModelResponse(data.model, data.response, 'success');
            }

            // 檢查是否所有模型都完成了
            const allComplete = this.currentResults.results.every(r =>
                r.status === 'success' || r.status === 'error'
            );

            if (allComplete) {
                this.showToast('所有 LLM 回應完成！', 'success');
                document.getElementById('send-button').disabled = false;
                document.getElementById('export-button').disabled = false;
                document.getElementById('compare-button').disabled = false;
                this.showLoading(false);
            }
        }
    }

    handleError(data) {
        console.error('Extension error:', data);

        if (this.currentResults) {
            const modelResult = this.currentResults.results.find(r => r.model === data.model);
            if (modelResult) {
                modelResult.status = 'error';
                modelResult.response = `錯誤: ${data.error}`;

                // 更新顯示
                this.updateModelResponse(data.model, `錯誤: ${data.error}`, 'error');
            }
        }

        this.showToast(`${data.model} 發生錯誤: ${data.error}`, 'error');
    }

    updateModelResponse(model, response, status) {
        const resultCard = document.querySelector(`[data-model="${model}"]`);
        if (resultCard) {
            const responseElement = resultCard.querySelector('.response-content');
            const statusElement = resultCard.querySelector('.model-status');

            if (responseElement) {
                responseElement.textContent = response;
            }

            if (statusElement) {
                statusElement.textContent = status === 'streaming' ? '生成中...' :
                                         status === 'success' ? '完成' :
                                         status === 'error' ? '錯誤' : '處理中...';
                statusElement.className = `model-status status-${status}`;
            }
        }
    }

    getSelectedModels() {
        const checkboxes = document.querySelectorAll('.model-checkboxes input[type="checkbox"]:checked');
        return Array.from(checkboxes).map(cb => cb.value);
    }
    
    showLoading(show) {
        const loadingIndicator = document.getElementById('loading-indicator');
        loadingIndicator.style.display = show ? 'block' : 'none';
    }
    
    clearResults() {
        const resultsContainer = document.getElementById('results-container');
        resultsContainer.innerHTML = '';
        
        // Disable export and compare buttons
        document.getElementById('export-button').disabled = true;
        document.getElementById('compare-button').disabled = true;
    }
    
    displayResults(data) {
        const resultsContainer = document.getElementById('results-container');
        
        // Parse the MCP response
        let mcpResults = {};
        try {
            if (data.data && data.data.result && data.data.result.length > 0) {
                const resultText = data.data.result[0].text;
                mcpResults = this.parseMCPResults(resultText);
            }
        } catch (error) {
            console.error('Error parsing MCP results:', error);
        }
        
        // Display results for each model
        data.models.forEach(model => {
            const resultCard = this.createResultCard(model, mcpResults[model] || {});
            resultsContainer.appendChild(resultCard);
        });
    }
    
    parseMCPResults(resultText) {
        const results = {};
        const modelConfigs = {
            'chatgpt': 'ChatGPT',
            'claude': 'Claude',
            'gemini': 'Gemini',
            'grok': 'Grok'
        };
        
        // Simple parsing - this could be improved with more sophisticated parsing
        for (const [modelKey, modelName] of Object.entries(modelConfigs)) {
            const modelSection = resultText.split(`## ${modelName}`)[1];
            if (modelSection) {
                const nextSection = modelSection.split('## ')[0];
                
                if (nextSection.includes('❌ Send Error:')) {
                    results[modelKey] = {
                        status: 'error',
                        error: nextSection.split('❌ Send Error:')[1].split('\n')[0].trim()
                    };
                } else if (nextSection.includes('⚠️ Response Error:')) {
                    results[modelKey] = {
                        status: 'error',
                        error: nextSection.split('⚠️ Response Error:')[1].split('\n')[0].trim()
                    };
                } else if (nextSection.includes('✅ Response:')) {
                    results[modelKey] = {
                        status: 'success',
                        response: nextSection.split('✅ Response:')[1].split('\n')[0].trim()
                    };
                } else if (nextSection.includes('⏳ Response:')) {
                    results[modelKey] = {
                        status: 'loading',
                        message: '回應生成中...'
                    };
                }
            }
        }
        
        return results;
    }
    
    createResultCard(model, result) {
        const modelNames = {
            'chatgpt': 'ChatGPT',
            'claude': 'Claude',
            'gemini': 'Gemini',
            'grok': 'Grok'
        };
        
        const card = document.createElement('div');
        card.className = `result-card ${result.status || 'loading'}`;
        
        const statusText = {
            'success': '成功',
            'error': '錯誤',
            'loading': '處理中'
        };
        
        card.innerHTML = `
            <div class="result-header">
                <div class="result-title">${modelNames[model] || model}</div>
                <div class="result-status ${result.status || 'loading'}">
                    ${statusText[result.status] || '處理中'}
                </div>
            </div>
            <div class="result-content">
                ${this.formatResultContent(result)}
            </div>
        `;
        
        return card;
    }
    
    formatResultContent(result) {
        if (result.error) {
            return `<div style="color: var(--error-color);">❌ ${result.error}</div>`;
        } else if (result.response) {
            return `<div>${result.response}</div>`;
        } else if (result.message) {
            return `<div style="color: var(--warning-color);">⏳ ${result.message}</div>`;
        } else {
            return '<div style="color: var(--text-secondary);">等待回應中...</div>';
        }
    }
    
    displayError(errorMessage) {
        const resultsContainer = document.getElementById('results-container');
        resultsContainer.innerHTML = `
            <div class="result-card error">
                <div class="result-header">
                    <div class="result-title">系統錯誤</div>
                    <div class="result-status error">錯誤</div>
                </div>
                <div class="result-content">
                    <div style="color: var(--error-color);">❌ ${errorMessage}</div>
                </div>
            </div>
        `;
    }
    
    clearForm() {
        document.getElementById('prompt-input').value = '';
        document.getElementById('prompt-input').style.height = 'auto';
        this.clearResults();
        this.currentResults = null;
    }
    
    async loadBrowserTabs() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/api/browser-tabs`);
            const data = await response.json();
            
            if (data.success) {
                this.displayBrowserTabs(data.data);
            } else {
                throw new Error(data.message || 'Failed to load browser tabs');
            }
            
        } catch (error) {
            console.error('Error loading browser tabs:', error);
            this.displayBrowserTabsError(error.message);
        }
    }
    
    displayBrowserTabs(tabsData) {
        const tabsContainer = document.getElementById('tabs-container');
        
        if (!tabsData || !tabsData.result || tabsData.result.length === 0) {
            tabsContainer.innerHTML = '<div class="tab-card">目前沒有開啟的瀏覽器分頁</div>';
            return;
        }
        
        const tabsText = tabsData.result[0].text;
        if (tabsText.includes('No browser tabs currently open')) {
            tabsContainer.innerHTML = '<div class="tab-card">目前沒有開啟的瀏覽器分頁</div>';
            return;
        }
        
        // Parse tabs information
        const tabLines = tabsText.split('\n').filter(line => line.startsWith('Tab '));
        
        if (tabLines.length === 0) {
            tabsContainer.innerHTML = '<div class="tab-card">目前沒有開啟的瀏覽器分頁</div>';
            return;
        }
        
        tabsContainer.innerHTML = tabLines.map(line => {
            const parts = line.split(': ');
            const tabId = parts[0].replace('Tab ', '');
            const urlAndTitle = parts[1] || 'Unknown';
            const [url, title] = urlAndTitle.split(' - ');
            
            return `
                <div class="tab-card">
                    <div class="tab-title">${title || 'No title'}</div>
                    <div class="tab-url">${url || 'Unknown URL'}</div>
                </div>
            `;
        }).join('');
    }
    
    displayBrowserTabsError(errorMessage) {
        const tabsContainer = document.getElementById('tabs-container');
        tabsContainer.innerHTML = `
            <div class="tab-card" style="border-color: var(--error-color);">
                <div class="tab-title" style="color: var(--error-color);">載入錯誤</div>
                <div class="tab-url">${errorMessage}</div>
            </div>
        `;
    }
    
    exportResults() {
        if (!this.currentResults) {
            this.showToast('沒有可匯出的結果', 'warning');
            return;
        }
        
        const exportData = {
            timestamp: new Date().toISOString(),
            prompt: document.getElementById('prompt-input').value,
            models: this.currentResults.models,
            results: this.currentResults.data
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `llm-results-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showToast('結果已匯出', 'success');
    }
    
    showCompareModal() {
        if (!this.currentResults) {
            this.showToast('沒有可比較的結果', 'warning');
            return;
        }
        
        const modal = document.getElementById('compare-modal');
        const compareContent = document.getElementById('compare-content');
        
        // Generate comparison content
        compareContent.innerHTML = this.generateComparisonContent();
        
        modal.style.display = 'flex';
    }
    
    hideCompareModal() {
        const modal = document.getElementById('compare-modal');
        modal.style.display = 'none';
    }
    
    generateComparisonContent() {
        // This is a simplified comparison - could be enhanced with more sophisticated analysis
        const prompt = document.getElementById('prompt-input').value;
        
        return `
            <div style="margin-bottom: 20px;">
                <h4>📝 原始 Prompt</h4>
                <div style="background-color: var(--background-color); padding: 15px; border-radius: var(--border-radius); margin-top: 10px;">
                    ${prompt}
                </div>
            </div>
            
            <div>
                <h4>📊 回應比較</h4>
                <p style="color: var(--text-secondary); margin-top: 10px;">
                    詳細的比較分析功能正在開發中。目前您可以在主頁面查看各個 LLM 的回應結果。
                </p>
                <p style="color: var(--text-secondary);">
                    未來版本將包含：
                </p>
                <ul style="color: var(--text-secondary); margin-left: 20px;">
                    <li>回應品質評分</li>
                    <li>相似度分析</li>
                    <li>關鍵詞提取</li>
                    <li>情感分析</li>
                    <li>回應長度統計</li>
                </ul>
            </div>
        `;
    }
    
    showToast(message, type = 'info') {
        const toastContainer = document.getElementById('toast-container');
        
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div style="font-weight: 600; margin-bottom: 5px;">
                ${type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'}
                ${type === 'success' ? '成功' : type === 'error' ? '錯誤' : type === 'warning' ? '警告' : '資訊'}
            </div>
            <div>${message}</div>
        `;
        
        toastContainer.appendChild(toast);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MultiLLMApp();
});
