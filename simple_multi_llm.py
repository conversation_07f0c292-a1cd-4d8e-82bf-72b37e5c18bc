#!/usr/bin/env python3
"""
簡化版多 LLM 處理器
避免編碼問題，直接返回結果
"""

import asyncio
import uuid
from typing import Dict, List, Optional
from playwright.async_api import async_playwright, Browser, Page

# LLM website configurations
LLM_CONFIGS = {
    'chatgpt': {
        'url': 'https://chatgpt.com',
        'name': 'ChatGPT',
        'prompt_selector': 'textarea[placeholder*="Message"]',
        'send_selector': 'button[data-testid="send-button"]',
        'response_selector': '.markdown'
    },
    'claude': {
        'url': 'https://claude.ai',
        'name': '<PERSON>',
        'prompt_selector': 'div[contenteditable="true"]',
        'send_selector': 'button[aria-label="Send Message"]',
        'response_selector': '.font-claude-message'
    },
    'gemini': {
        'url': 'https://gemini.google.com',
        'name': '<PERSON>',
        'prompt_selector': 'rich-textarea[placeholder*="Enter a prompt"]',
        'send_selector': 'button[aria-label="Send message"]',
        'response_selector': '.model-response-text'
    },
    'grok': {
        'url': 'https://x.com/i/grok',
        'name': 'Grok',
        'prompt_selector': 'textarea[placeholder*="Ask Grok"]',
        'send_selector': 'button[aria-label="Send"]',
        'response_selector': '.grok-response'
    }
}

class SimpleMultiLLMManager:
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.pages: Dict[str, Page] = {}
        self.playwright = None
        
    async def initialize(self, headless: bool = False):
        """Initialize the browser and pages for all LLMs."""
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=headless,
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )
            print("Multi-LLM browser initialized successfully")
            return True
        except Exception as e:
            print(f"Failed to initialize browser: {str(e)}")
            return False
    
    async def open_llm_pages(self, models: List[str]) -> Dict[str, bool]:
        """Open pages for specified LLM models."""
        results = {}
        
        for model in models:
            if model not in LLM_CONFIGS:
                print(f"Unknown model: {model}")
                results[model] = False
                continue
                
            try:
                config = LLM_CONFIGS[model]
                page = await self.browser.new_page()
                
                # Set user agent to avoid detection
                await page.set_extra_http_headers({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                })
                
                await page.goto(config['url'], wait_until='networkidle')
                await page.wait_for_timeout(2000)  # Wait for page to fully load
                
                self.pages[model] = page
                results[model] = True
                print(f"Successfully opened {config['name']} page")
                
            except Exception as e:
                print(f"Failed to open {model} page: {str(e)}")
                results[model] = False
                
        return results
    
    async def send_prompt_to_model(self, model: str, prompt: str) -> Dict[str, str]:
        """Send prompt to a specific LLM model."""
        if model not in self.pages:
            return {"error": f"Page for {model} not initialized"}
            
        try:
            config = LLM_CONFIGS[model]
            page = self.pages[model]
            
            # Wait for prompt input to be available
            await page.wait_for_selector(config['prompt_selector'], timeout=10000)
            
            # Clear any existing text and type the prompt
            await page.fill(config['prompt_selector'], prompt)
            await page.wait_for_timeout(500)
            
            # Click send button
            await page.click(config['send_selector'])
            print(f"Sent prompt to {config['name']}")
            
            # Wait for response (this is model-specific and may need adjustment)
            await page.wait_for_timeout(3000)
            
            return {"status": "sent", "model": model}
            
        except Exception as e:
            error_msg = f"Failed to send prompt to {model}: {str(e)}"
            print(error_msg)
            return {"error": error_msg}
    
    async def get_response_from_model(self, model: str) -> Dict[str, str]:
        """Get the latest response from a specific LLM model."""
        if model not in self.pages:
            return {"error": f"Page for {model} not initialized"}
            
        try:
            config = LLM_CONFIGS[model]
            page = self.pages[model]
            
            # Wait for response to appear
            await page.wait_for_selector(config['response_selector'], timeout=30000)
            
            # Get the response text
            response_elements = await page.query_selector_all(config['response_selector'])
            if response_elements:
                # Get the last response (most recent)
                response_text = await response_elements[-1].inner_text()
                return {"response": response_text, "model": model}
            else:
                return {"error": f"No response found for {model}"}
                
        except Exception as e:
            error_msg = f"Failed to get response from {model}: {str(e)}"
            print(error_msg)
            return {"error": error_msg}
    
    async def send_prompt_to_all(self, models: List[str], prompt: str) -> Dict[str, Dict]:
        """Send prompt to all specified models simultaneously."""
        tasks = []
        for model in models:
            if model in self.pages:
                tasks.append(self.send_prompt_to_model(model, prompt))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Format results
        formatted_results = {}
        for i, model in enumerate([m for m in models if m in self.pages]):
            if isinstance(results[i], Exception):
                formatted_results[model] = {"error": str(results[i])}
            else:
                formatted_results[model] = results[i]
                
        return formatted_results
    
    async def get_all_responses(self, models: List[str]) -> Dict[str, Dict]:
        """Get responses from all specified models."""
        tasks = []
        for model in models:
            if model in self.pages:
                tasks.append(self.get_response_from_model(model))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Format results
        formatted_results = {}
        for i, model in enumerate([m for m in models if m in self.pages]):
            if isinstance(results[i], Exception):
                formatted_results[model] = {"error": str(results[i])}
            else:
                formatted_results[model] = results[i]
                
        return formatted_results
    
    async def cleanup(self):
        """Clean up browser resources."""
        try:
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            print("Multi-LLM browser cleanup completed")
        except Exception as e:
            print(f"Error during cleanup: {str(e)}")

# Global manager instance
_manager = None

async def get_manager() -> SimpleMultiLLMManager:
    """Get or create the global SimpleMultiLLMManager instance."""
    global _manager
    if _manager is None:
        _manager = SimpleMultiLLMManager()
        await _manager.initialize(headless=False)  # Use non-headless for better compatibility
    return _manager

async def handle_simple_multi_llm_prompt(prompt: str, models: List[str] = None) -> str:
    """Handle multi-LLM prompt sending and response collection."""
    if models is None:
        models = ['chatgpt', 'claude', 'gemini', 'grok']

    if not prompt:
        return "❌ Error: No prompt provided"

    try:
        manager = await get_manager()

        # Open pages for models that aren't already open
        models_to_open = [m for m in models if m not in manager.pages]
        if models_to_open:
            print(f"Opening pages for models: {models_to_open}")
            open_results = await manager.open_llm_pages(models_to_open)

            # Filter out models that failed to open
            successful_models = [m for m in models if open_results.get(m, False) or m in manager.pages]
        else:
            successful_models = [m for m in models if m in manager.pages]

        if not successful_models:
            return "❌ Error: No LLM pages could be opened"

        # Send prompt to all models
        print(f"Sending prompt to models: {successful_models}")
        send_results = await manager.send_prompt_to_all(successful_models, prompt)

        # Wait a bit for responses to generate
        await asyncio.sleep(5)

        # Get responses from all models
        print("Collecting responses from all models")
        response_results = await manager.get_all_responses(successful_models)

        # Format the results
        result_text = f"🚀 Multi-LLM Prompt Results\n"
        result_text += f"📝 Prompt: {prompt}\n"
        result_text += f"🎯 Models: {', '.join(successful_models)}\n\n"

        for model in successful_models:
            config = LLM_CONFIGS.get(model, {})
            model_name = config.get('name', model.title())

            result_text += f"## {model_name}\n"

            # Check send status
            send_result = send_results.get(model, {})
            if 'error' in send_result:
                result_text += f"❌ Send Error: {send_result['error']}\n\n"
                continue

            # Check response
            response_result = response_results.get(model, {})
            if 'error' in response_result:
                result_text += f"⚠️ Response Error: {response_result['error']}\n\n"
            elif 'response' in response_result:
                response_text = response_result['response'][:500]  # Limit response length
                if len(response_result['response']) > 500:
                    response_text += "... (truncated)"
                result_text += f"✅ Response: {response_text}\n\n"
            else:
                result_text += f"⏳ Response: Still generating...\n\n"

        return result_text

    except Exception as e:
        error_msg = f"❌ Error in multi-LLM handler: {str(e)}"
        print(error_msg)
        return error_msg

# Test function
async def test_simple_handler():
    """Test the simple handler"""
    result = await handle_simple_multi_llm_prompt("Hello, please say hi!", ["chatgpt"])
    print("Test result:")
    print(result)

if __name__ == "__main__":
    asyncio.run(test_simple_handler())
