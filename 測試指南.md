# Browser MCP Multi-LLM 測試指南

## 🧪 測試概述

本指南將引導您完成Browser MCP Multi-LLM系統的完整測試流程，確保所有功能正常運作。

## 📋 測試前準備

### 1. 系統需求檢查
- ✅ Chrome瀏覽器 88+ 版本
- ✅ 穩定的網路連接
- ✅ 各AI平台的有效帳號

### 2. 帳號準備
請確保您擁有以下帳號並能正常登入：
- **OpenAI帳號** (ChatGPT)
- **Anthropic帳號** (Claude)
- **Google帳號** (Gemini)
- **X帳號** (Grok)

## 🚀 快速測試流程

### 步驟1: 執行自動測試
```bash
雙擊 test-system.bat
```

這將自動：
1. 開啟Chrome擴充功能頁面
2. 開啟所有AI網站供登入
3. 啟動控制面板

### 步驟2: 載入擴充功能
1. 在Chrome擴充功能頁面中
2. 開啟「開發人員模式」
3. 點擊「載入未封裝項目」
4. 選擇 `browser-extension` 資料夾
5. 確認看到「Browser MCP Multi-LLM Controller」

### 步驟3: 登入所有AI網站
依次登入以下網站：
- https://chat.openai.com
- https://claude.ai
- https://gemini.google.com
- https://grok.x.ai

### 步驟4: 測試控制面板
1. 開啟 `frontend/forum.html`
2. 檢查右上角狀態指示器
3. 確認「擴充功能」和「瀏覽器控制」都顯示為線上

## 🔍 詳細測試項目

### A. 擴充功能測試

#### A1. 載入測試
- [ ] 擴充功能成功載入
- [ ] 無錯誤訊息
- [ ] 工具列顯示擴充功能圖標

#### A2. 權限測試
- [ ] 可以訪問AI網站
- [ ] 可以注入content scripts
- [ ] 可以與background script通訊

#### A3. Popup測試
- [ ] 點擊工具列圖標開啟popup
- [ ] 顯示各AI網站狀態
- [ ] 「開啟控制面板」按鈕正常
- [ ] 「重新整理狀態」按鈕正常

### B. 控制面板測試

#### B1. 介面測試
- [ ] 頁面正常載入
- [ ] 狀態指示器顯示正確
- [ ] 所有UI元素正常顯示
- [ ] 響應式設計正常

#### B2. 連接測試
- [ ] 擴充功能連接成功
- [ ] 狀態指示器顯示「線上」
- [ ] 無連接錯誤訊息

#### B3. 模型選擇測試
- [ ] 可以勾選/取消勾選AI模型
- [ ] 選擇狀態正確保存
- [ ] 至少選擇一個模型時發送按鈕可用

### C. AI網站整合測試

#### C1. ChatGPT測試
- [ ] 自動開啟chat.openai.com分頁
- [ ] 偵測登入狀態
- [ ] 找到輸入框和發送按鈕
- [ ] 成功發送測試prompt
- [ ] 接收並顯示回應

#### C2. Claude測試
- [ ] 自動開啟claude.ai分頁
- [ ] 偵測登入狀態
- [ ] 找到輸入框和發送按鈕
- [ ] 成功發送測試prompt
- [ ] 接收並顯示回應

#### C3. Gemini測試
- [ ] 自動開啟gemini.google.com分頁
- [ ] 偵測登入狀態
- [ ] 找到輸入框和發送按鈕
- [ ] 成功發送測試prompt
- [ ] 接收並顯示回應

#### C4. Grok測試
- [ ] 自動開啟grok.x.ai分頁
- [ ] 偵測登入狀態
- [ ] 找到輸入框和發送按鈕
- [ ] 成功發送測試prompt
- [ ] 接收並顯示回應

### D. 功能測試

#### D1. 單一模型測試
測試prompt: "請說一個笑話"
- [ ] 選擇單一AI模型
- [ ] 發送prompt
- [ ] 收到正確回應
- [ ] 回應格式正確

#### D2. 多模型同步測試
測試prompt: "解釋什麼是人工智慧"
- [ ] 選擇多個AI模型
- [ ] 同時發送prompt
- [ ] 所有選中的AI都收到prompt
- [ ] 所有回應都正確顯示
- [ ] 回應格式一致

#### D3. 串流顯示測試
- [ ] 回應即時顯示
- [ ] 串流更新流暢
- [ ] 無延遲或卡頓
- [ ] 完整回應正確顯示

#### D4. 錯誤處理測試
- [ ] 未登入狀態正確處理
- [ ] 網路錯誤正確處理
- [ ] 分頁關閉正確處理
- [ ] 重試機制正常運作

## 🐛 常見問題測試

### 問題1: 擴充功能無法載入
**測試步驟:**
1. 檢查manifest.json語法
2. 檢查檔案路徑
3. 查看Chrome錯誤訊息

**預期結果:** 能夠識別並解決載入問題

### 問題2: AI網站無回應
**測試步驟:**
1. 檢查登入狀態
2. 檢查DOM選擇器
3. 查看Console錯誤

**預期結果:** 能夠識別問題並提供解決方案

### 問題3: 回應顯示異常
**測試步驟:**
1. 重新載入控制面板
2. 重新載入擴充功能
3. 清除瀏覽器快取

**預期結果:** 回應正常顯示

## 📊 效能測試

### P1. 記憶體使用測試
- [ ] 長時間運行無記憶體洩漏
- [ ] 多次對話後記憶體穩定
- [ ] 分頁關閉後資源正確釋放

### P2. 響應時間測試
- [ ] prompt發送延遲 < 1秒
- [ ] 回應開始時間 < 3秒
- [ ] UI更新延遲 < 100ms

### P3. 並發測試
- [ ] 同時處理4個AI回應
- [ ] 無競爭條件
- [ ] 回應順序正確

## ✅ 測試完成檢查清單

### 基本功能
- [ ] 擴充功能正常載入
- [ ] 控制面板正常運作
- [ ] 所有AI網站可以連接
- [ ] 單一模型對話正常
- [ ] 多模型同步對話正常

### 進階功能
- [ ] 串流顯示正常
- [ ] 錯誤處理正確
- [ ] 重試機制有效
- [ ] 狀態監控準確
- [ ] 效能表現良好

### 用戶體驗
- [ ] 介面直觀易用
- [ ] 操作流程順暢
- [ ] 錯誤訊息清楚
- [ ] 回應格式一致
- [ ] 整體體驗良好

## 🎯 測試建議

1. **循序漸進**: 先測試基本功能，再測試進階功能
2. **多次測試**: 重複測試確保穩定性
3. **不同場景**: 測試各種使用場景
4. **記錄問題**: 詳細記錄遇到的問題
5. **驗證修復**: 確認問題修復後重新測試

## 📝 測試報告模板

```
測試日期: ___________
測試人員: ___________
Chrome版本: _________

基本功能測試:
□ 通過 □ 失敗 - 擴充功能載入
□ 通過 □ 失敗 - 控制面板
□ 通過 □ 失敗 - AI網站連接
□ 通過 □ 失敗 - 單一模型對話
□ 通過 □ 失敗 - 多模型同步

問題記錄:
1. ________________________
2. ________________________
3. ________________________

整體評價:
□ 優秀 □ 良好 □ 普通 □ 需改進

建議:
_________________________________
```

---

**注意**: 測試過程中如遇到問題，請參考「故障排除」章節或查看瀏覽器開發者工具的Console錯誤訊息。
