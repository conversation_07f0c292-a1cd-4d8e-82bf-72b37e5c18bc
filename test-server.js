console.log('Starting test server...');

try {
    const express = require('express');
    console.log('Express loaded successfully');
    
    const app = express();
    const PORT = 3000;
    
    app.get('/', (req, res) => {
        res.send('<h1>Test Server Working!</h1>');
    });
    
    app.listen(PORT, () => {
        console.log(`Test server running on http://localhost:${PORT}`);
    });
    
} catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
}
