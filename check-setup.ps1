# Browser MCP 環境檢查腳本
# 使用方式: .\check-setup.ps1

Write-Host "🚀 Browser MCP 多 LLM 系統環境檢查" -ForegroundColor Cyan
Write-Host "=" * 50

# 檢查 PowerShell 版本
Write-Host "`n🔍 檢查 PowerShell 版本..." -ForegroundColor Yellow
$psVersion = $PSVersionTable.PSVersion
Write-Host "PowerShell 版本: $psVersion"
if ($psVersion.Major -ge 5) {
    Write-Host "✅ PowerShell 版本符合要求 (5.1+)" -ForegroundColor Green
} else {
    Write-Host "❌ PowerShell 版本過低，需要 5.1+" -ForegroundColor Red
}

# 檢查 Python 版本
Write-Host "`n🔍 檢查 Python 版本..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Python 版本: $pythonVersion"
    if ($pythonVersion -match "Python 3\.1[1-9]" -or $pythonVersion -match "Python 3\.[2-9]") {
        Write-Host "✅ Python 版本符合要求 (3.11+)" -ForegroundColor Green
    } else {
        Write-Host "❌ Python 版本不符合要求，需要 3.11+" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Python 未安裝或不在 PATH 中" -ForegroundColor Red
}

# 檢查 uv 安裝
Write-Host "`n🔍 檢查 uv 包管理器..." -ForegroundColor Yellow
try {
    $uvVersion = uv --version 2>&1
    Write-Host "uv 版本: $uvVersion"
    Write-Host "✅ uv 已安裝" -ForegroundColor Green
} catch {
    Write-Host "❌ uv 未安裝，請執行: irm https://astral.sh/uv/install.ps1 | iex" -ForegroundColor Red
}

# 檢查專案結構
Write-Host "`n🔍 檢查專案結構..." -ForegroundColor Yellow
$requiredFiles = @(
    "pyproject.toml",
    "webEvalAgent/mcp_server.py",
    "webEvalAgent/src/utils.py"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file 存在" -ForegroundColor Green
    } else {
        Write-Host "❌ $file 不存在" -ForegroundColor Red
    }
}

# 檢查虛擬環境
Write-Host "`n🔍 檢查虛擬環境..." -ForegroundColor Yellow
if (Test-Path ".venv") {
    Write-Host "✅ .venv 目錄存在" -ForegroundColor Green
    
    # 檢查虛擬環境中的包
    try {
        $packages = uv pip list 2>&1
        if ($packages -match "mcp") {
            Write-Host "✅ MCP 包已安裝" -ForegroundColor Green
        } else {
            Write-Host "⚠️  MCP 包未安裝，請執行: uv pip install -e ." -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️  無法檢查已安裝的包" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠️  .venv 目錄不存在，請執行: uv venv .venv" -ForegroundColor Yellow
}

# 檢查埠號占用
Write-Host "`n🔍 檢查埠號 5000..." -ForegroundColor Yellow
try {
    $port5000 = netstat -ano | findstr ":5000"
    if ($port5000) {
        Write-Host "⚠️  埠號 5000 被占用:" -ForegroundColor Yellow
        Write-Host $port5000
        $pid = ($port5000 -split '\s+')[-1]
        Write-Host "如需釋放，請執行: taskkill /PID $pid /F" -ForegroundColor Yellow
    } else {
        Write-Host "✅ 埠號 5000 可用" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️  無法檢查埠號狀態" -ForegroundColor Yellow
}

# 檢查環境變數
Write-Host "`n🔍 檢查環境變數..." -ForegroundColor Yellow
if ($env:OPERATIVE_API_KEY) {
    $keyPreview = $env:OPERATIVE_API_KEY.Substring(0, [Math]::Min(10, $env:OPERATIVE_API_KEY.Length)) + "..."
    Write-Host "✅ OPERATIVE_API_KEY 已設定: $keyPreview" -ForegroundColor Green
} else {
    Write-Host "⚠️  OPERATIVE_API_KEY 未設定，將使用離線模式" -ForegroundColor Yellow
    Write-Host "如需雲端模式，請執行: `$Env:OPERATIVE_API_KEY='op-your-key'" -ForegroundColor Yellow
}

# 檢查 Playwright
Write-Host "`n🔍 檢查 Playwright 瀏覽器..." -ForegroundColor Yellow
try {
    $playwrightCheck = uv run playwright --version 2>&1
    if ($playwrightCheck -match "Version") {
        Write-Host "✅ Playwright 已安裝" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Playwright 未安裝，請執行: uv run playwright install chromium" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  無法檢查 Playwright 狀態" -ForegroundColor Yellow
}

# 總結
Write-Host "`n" + "=" * 50
Write-Host "🎯 快速啟動指令:" -ForegroundColor Cyan
Write-Host ""
Write-Host "# 離線模式 (推薦用於開發)" -ForegroundColor Gray
Write-Host "uv run python -m webEvalAgent.mcp_server --local --host 0.0.0.0 --port 5000" -ForegroundColor White
Write-Host ""
Write-Host "# 雲端模式 (需要 API Key)" -ForegroundColor Gray
Write-Host "`$Env:OPERATIVE_API_KEY='op-your-key'" -ForegroundColor White
Write-Host "uv run python -m webEvalAgent.mcp_server --host 0.0.0.0 --port 5000" -ForegroundColor White
Write-Host ""
Write-Host "# 健康檢查" -ForegroundColor Gray
Write-Host "curl http://localhost:5000/health" -ForegroundColor White

Write-Host "`n🔗 相關文件:" -ForegroundColor Cyan
Write-Host "- PRD: PRD-Browser-MCP-Multi-LLM.md"
Write-Host "- GitHub: https://github.com/Operative-Sh/web-eval-agent"

Write-Host "`n✨ 檢查完成！" -ForegroundColor Green
