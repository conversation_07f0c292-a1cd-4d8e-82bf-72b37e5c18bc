<!DOCTYPE html>
<html>
<head>
    <title>LLM Forum</title>
</head>
<body>
    <h1>LLM Forum</h1>

    <label for="prompt">Prompt:</label><br>
    <textarea id="prompt" name="prompt" rows="4" cols="50"></textarea><br><br>

    <label>Select LLM:</label><br>
    <button onclick="sendPrompt('ChatGPT')">ChatGPT</button>
    <button onclick="sendPrompt('Claude')">Claude</button>
    <button onclick="sendPrompt('Gemini')">Gemini</button>
    <button onclick="sendPrompt('Grok')">Grok</button><br><br>

    <h2>Responses:</h2>
    <div id="chatgpt_response">ChatGPT:</div>
    <div id="claude_response">Claude:</div>
    <div id="gemini_response">Gemini:</div>
    <div id="grok_response">Grok:</div>

    <script>
        async function sendPrompt(browser) {
            const prompt = document.getElementById('prompt').value;
            const responseDivId = browser.toLowerCase() + '_response';
            document.getElementById(responseDivId).innerText = browser + ': Sending...';

            const data = {
                browser: browser,
                prompt: prompt
            };

            try {
                const response = await fetch('/api/send', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                document.getElementById(responseDivId).innerText = browser + ': ' + result.response;
            } catch (error) {
                document.getElementById(responseDivId).innerText = browser + ': Error - ' + error;
            }
                });
                const result = await response.json();
                document.getElementById(responseDivId).innerText = browser + ': ' + result.response;
            } catch (error) {
                document.getElementById(responseDivId).innerText = browser + ': Error - ' + error;
            }
        }
    </script>
</body>
</html>
