#!/usr/bin/env python3
"""
測試 MCP HTTP 橋接服務器
"""

import requests
import json
import time

def test_mcp_bridge():
    """測試 MCP 橋接服務器"""
    
    print("🧪 測試 MCP HTTP 橋接服務器")
    print("=" * 50)
    
    # 測試服務器是否運行
    server_url = "http://localhost:5000"
    
    print(f"📡 測試服務器連接: {server_url}")
    
    try:
        # 測試連接
        response = requests.get(server_url, timeout=5)
        print(f"❌ 服務器回應了 GET 請求（應該只支援 POST）")
    except requests.exceptions.ConnectionError:
        print(f"❌ 無法連接到服務器，請確保服務器正在運行")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ 連接超時")
        return False
    except Exception as e:
        print(f"⚠️  GET 請求異常（正常，因為只支援 POST）: {e}")
    
    # 測試 send_prompt_to_llms 端點
    print(f"\n📝 測試 send_prompt_to_llms 端點")
    
    test_data = {
        "prompt": "請說一個簡短的笑話",
        "models": ["chatgpt"]  # 只測試一個模型
    }
    
    try:
        print(f"🚀 發送測試請求...")
        print(f"請求數據: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
        
        response = requests.post(
            f"{server_url}/send_prompt_to_llms",
            json=test_data,
            timeout=60  # 給足夠時間處理
        )
        
        print(f"\n📊 回應狀態碼: {response.status_code}")
        print(f"📄 回應內容類型: {response.headers.get('content-type', 'unknown')}")
        
        if response.status_code == 200:
            print(f"✅ 請求成功！")
            print(f"📝 回應內容:")
            print("-" * 40)
            print(response.text)
            print("-" * 40)
            return True
        else:
            print(f"❌ 請求失敗，狀態碼: {response.status_code}")
            print(f"錯誤內容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ 無法連接到服務器")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ 請求超時（60秒）")
        return False
    except Exception as e:
        print(f"❌ 請求異常: {e}")
        return False

def test_invalid_requests():
    """測試無效請求的處理"""
    
    print(f"\n🔍 測試錯誤處理")
    print("=" * 30)
    
    server_url = "http://localhost:5000"
    
    # 測試空 prompt
    print(f"📝 測試空 prompt...")
    try:
        response = requests.post(
            f"{server_url}/send_prompt_to_llms",
            json={"prompt": "", "models": ["chatgpt"]},
            timeout=10
        )
        print(f"狀態碼: {response.status_code}")
        print(f"回應: {response.text}")
    except Exception as e:
        print(f"異常: {e}")
    
    # 測試無效 JSON
    print(f"\n📝 測試無效 JSON...")
    try:
        response = requests.post(
            f"{server_url}/send_prompt_to_llms",
            data="invalid json",
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        print(f"狀態碼: {response.status_code}")
        print(f"回應: {response.text}")
    except Exception as e:
        print(f"異常: {e}")
    
    # 測試不存在的端點
    print(f"\n📝 測試不存在的端點...")
    try:
        response = requests.post(
            f"{server_url}/nonexistent",
            json={"test": "data"},
            timeout=10
        )
        print(f"狀態碼: {response.status_code}")
        print(f"回應: {response.text}")
    except Exception as e:
        print(f"異常: {e}")

def main():
    """主函數"""
    print("🚀 MCP HTTP 橋接服務器測試工具")
    print("=" * 60)
    print()
    
    print("⚠️  請確保以下條件已滿足：")
    print("1. MCP HTTP 橋接服務器正在運行 (python mcp-http-bridge.py)")
    print("2. Web Eval Agent 已正確安裝")
    print("3. 網路連接正常")
    print()
    
    input("按 Enter 鍵開始測試...")
    print()
    
    # 基本功能測試
    success = test_mcp_bridge()
    
    if success:
        print(f"\n🎉 基本功能測試通過！")
        
        # 錯誤處理測試
        test_invalid_requests()
        
        print(f"\n✅ 所有測試完成")
        print(f"💡 如果看到正常的回應，說明系統工作正常")
        print(f"🌐 現在可以打開前端介面 (forum.html) 進行完整測試")
    else:
        print(f"\n❌ 基本功能測試失敗")
        print(f"🔧 請檢查：")
        print(f"   1. MCP 橋接服務器是否正在運行")
        print(f"   2. Web Eval Agent 是否正確安裝")
        print(f"   3. Python 環境是否正確配置")
        print(f"   4. 網路連接是否正常")

if __name__ == "__main__":
    main()
