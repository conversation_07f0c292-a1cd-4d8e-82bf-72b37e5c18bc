// ChatGPT Content Script

class ChatGPTController {
    constructor() {
        this.model = 'chatgpt';
        this.isProcessing = false;
        this.responseObserver = null;
        this.lastResponseLength = 0;
        
        this.init();
    }
    
    init() {
        console.log('ChatGPT content script loaded');
        
        // 通知background script已準備就緒
        chrome.runtime.sendMessage({
            type: 'CONTENT_SCRIPT_READY',
            model: this.model
        });
        
        // 監聽來自background的訊息
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true;
        });
        
        // 等待頁面完全載入
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupPage());
        } else {
            this.setupPage();
        }
    }
    
    setupPage() {
        // 檢查是否已登入
        this.checkLoginStatus();
    }
    
    checkLoginStatus() {
        // ChatGPT登入檢查邏輯
        const loginIndicators = [
            'button[data-testid="send-button"]',
            'textarea[data-id="root"]',
            '#prompt-textarea',
            'form textarea'
        ];

        const loginFailureIndicators = [
            '.auth-form',
            '[data-testid="login-button"]',
            'button:contains("Log in")',
            '.login-container'
        ];

        const isLoggedIn = loginIndicators.some(selector =>
            document.querySelector(selector) !== null
        );

        const needsLogin = loginFailureIndicators.some(selector =>
            document.querySelector(selector) !== null
        );

        if (needsLogin) {
            console.log('ChatGPT: User needs to log in');
            this.sendError('請先登入ChatGPT - 偵測到登入頁面');
            return false;
        }

        if (isLoggedIn) {
            console.log('ChatGPT: User is logged in');
            return true;
        } else {
            console.log('ChatGPT: Login status unclear, will attempt to proceed');
            return true; // 不確定時允許嘗試
        }
    }
    
    handleMessage(message, sender, sendResponse) {
        switch (message.type) {
            case 'SEND_PROMPT':
                this.sendPrompt(message.prompt);
                break;
        }
    }
    
    async sendPrompt(prompt) {
        if (this.isProcessing) {
            this.sendError('ChatGPT正在處理中，請稍候');
            return;
        }

        const maxRetries = 3;
        let retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                this.isProcessing = true;
                this.lastResponseLength = 0;

                // 檢查登入狀態
                if (!this.checkLoginStatus()) {
                    throw new Error('未登入ChatGPT');
                }

                // 尋找輸入框
                const inputBox = this.findInputBox();
                if (!inputBox) {
                    throw new Error('找不到ChatGPT輸入框');
                }

                // 清空並輸入prompt
                await this.typeInInput(inputBox, prompt);

                // 設置回應監聽器
                this.setupResponseListener();

                // 點擊發送按鈕
                const sendButton = this.findSendButton();
                if (!sendButton) {
                    throw new Error('找不到ChatGPT發送按鈕');
                }

                sendButton.click();

                // 等待一下確認發送成功
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 檢查是否開始處理
                if (this.isResponseStarted()) {
                    return; // 成功發送
                } else {
                    throw new Error('發送後未偵測到回應開始');
                }

            } catch (error) {
                retryCount++;
                console.error(`ChatGPT send prompt error (attempt ${retryCount}):`, error);

                if (retryCount >= maxRetries) {
                    this.sendError(`發送失敗，已重試${maxRetries}次: ${error.message}`);
                    this.isProcessing = false;
                    return;
                }

                // 重置狀態並等待重試
                this.isProcessing = false;
                await new Promise(resolve => setTimeout(resolve, 2000 * retryCount));
            }
        }
    }
    
    findInputBox() {
        const selectors = [
            '#prompt-textarea',
            'textarea[data-id="root"]',
            'form textarea',
            'div[contenteditable="true"]'
        ];
        
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) return element;
        }
        
        return null;
    }
    
    findSendButton() {
        const selectors = [
            'button[data-testid="send-button"]',
            'button[aria-label="Send prompt"]',
            'form button[type="submit"]',
            'button:has(svg)'
        ];
        
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element && !element.disabled) return element;
        }
        
        return null;
    }
    
    async typeInInput(inputBox, text) {
        // 清空輸入框
        inputBox.value = '';
        inputBox.textContent = '';
        
        // 觸發focus事件
        inputBox.focus();
        
        // 模擬輸入
        if (inputBox.tagName === 'TEXTAREA' || inputBox.tagName === 'INPUT') {
            inputBox.value = text;
            inputBox.dispatchEvent(new Event('input', { bubbles: true }));
        } else {
            // contenteditable div
            inputBox.textContent = text;
            inputBox.dispatchEvent(new Event('input', { bubbles: true }));
        }
        
        // 等待一下讓UI更新
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    setupResponseListener() {
        // 尋找回應容器
        const responseContainer = this.findResponseContainer();
        if (!responseContainer) {
            console.warn('ChatGPT: Could not find response container');
            return;
        }
        
        // 設置MutationObserver監聽回應
        this.responseObserver = new MutationObserver((mutations) => {
            this.handleResponseMutation(mutations);
        });
        
        this.responseObserver.observe(responseContainer, {
            childList: true,
            subtree: true,
            characterData: true
        });
    }
    
    findResponseContainer() {
        const selectors = [
            '[data-testid="conversation-turn-3"]',
            '.group:last-child',
            'main > div > div > div:last-child',
            '[role="presentation"]:last-child'
        ];
        
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) return element;
        }
        
        return document.body;
    }
    
    handleResponseMutation(mutations) {
        // 獲取最新的回應內容
        const responseText = this.extractLatestResponse();
        
        if (responseText && responseText.length > this.lastResponseLength) {
            const newChunk = responseText.substring(this.lastResponseLength);
            this.lastResponseLength = responseText.length;
            
            // 發送新的文字塊
            chrome.runtime.sendMessage({
                type: 'RESPONSE_CHUNK',
                model: this.model,
                chunk: newChunk
            });
        }
        
        // 檢查是否完成
        if (this.isResponseComplete()) {
            this.handleResponseComplete(responseText);
        }
    }
    
    extractLatestResponse() {
        // 嘗試多種選擇器來獲取最新回應
        const selectors = [
            '.group:last-child .markdown',
            '.group:last-child [data-message-author-role="assistant"]',
            '[data-testid="conversation-turn-3"] .markdown',
            '.group:last-child p'
        ];
        
        for (const selector of selectors) {
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0) {
                const lastElement = elements[elements.length - 1];
                return lastElement.textContent || lastElement.innerText;
            }
        }
        
        return '';
    }
    
    isResponseStarted() {
        // 檢查是否有停止生成按鈕（表示正在生成）
        const stopButton = document.querySelector('button[aria-label="Stop generating"]');
        if (stopButton) return true;

        // 檢查發送按鈕是否被禁用（表示正在處理）
        const sendButton = this.findSendButton();
        if (sendButton && sendButton.disabled) return true;

        // 檢查是否有新的回應內容
        const latestResponse = this.extractLatestResponse();
        return latestResponse && latestResponse.length > 0;
    }

    isResponseComplete() {
        // 檢查是否有停止生成按鈕（表示正在生成）
        const stopButton = document.querySelector('button[aria-label="Stop generating"]');
        if (stopButton) return false;

        // 檢查發送按鈕是否可用（表示生成完成）
        const sendButton = this.findSendButton();
        return sendButton && !sendButton.disabled;
    }
    
    handleResponseComplete(fullResponse) {
        if (this.responseObserver) {
            this.responseObserver.disconnect();
            this.responseObserver = null;
        }
        
        this.isProcessing = false;
        
        chrome.runtime.sendMessage({
            type: 'RESPONSE_COMPLETE',
            model: this.model,
            response: fullResponse
        });
    }
    
    sendError(errorMessage) {
        chrome.runtime.sendMessage({
            type: 'ERROR',
            model: this.model,
            error: errorMessage
        });
    }
}

// 初始化ChatGPT控制器
const chatgptController = new ChatGPTController();
