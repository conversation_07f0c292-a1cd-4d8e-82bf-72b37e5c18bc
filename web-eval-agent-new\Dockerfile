# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
# Use Python 3.11 as the base image
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies including curl for uv installation
RUN apt-get update && apt-get install -y \
    curl \
    build-essential \
    libglib2.0-0 \
    libnss3 \
    libnspr4 \
    libdbus-1-3 \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libatspi2.0-0 \
    libxcomposite1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxrandr2 \
    libgbm1 \
    libxkbcommon0 \
    libasound2 \
    libcups2 \
    libpango-1.0-0 \
    libcairo2 \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install -r requirements.txt

# Install playwright browsers
RUN pip install playwright && playwright install chromium
RUN playwright install-deps

# Copy application code
COPY . .

# Set environment variable for API key (should be overridden at runtime)
ENV OPERATIVE_API_KEY="your_api_key_here"

# Run the MCP server
CMD ["python", "mcp_server.py"]
