# Browser MCP 多 LLM 系統環境檢查腳本
# 使用方式: .\check-setup.ps1

param(
    [switch]$Fix,
    [switch]$Verbose,
    [switch]$Help
)

if ($Help) {
    Write-Host "Browser MCP 多 LLM 系統環境檢查腳本" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "使用方式:" -ForegroundColor Yellow
    Write-Host "  .\check-setup.ps1           # 檢查環境"
    Write-Host "  .\check-setup.ps1 -Fix      # 檢查並嘗試修復問題"
    Write-Host "  .\check-setup.ps1 -Verbose  # 詳細輸出"
    Write-Host "  .\check-setup.ps1 -Help     # 顯示幫助"
    Write-Host ""
    Write-Host "參數說明:" -ForegroundColor Yellow
    Write-Host "  -Fix       嘗試自動修復發現的問題"
    Write-Host "  -Verbose   顯示詳細檢查資訊"
    Write-Host "  -Help      顯示此幫助訊息"
    exit 0
}

Write-Host "🔍 Browser MCP 多 LLM 系統環境檢查" -ForegroundColor Cyan
Write-Host "=" * 50

$issues = @()
$warnings = @()

function Check-Command {
    param(
        [string]$Command,
        [string]$Name,
        [string]$InstallUrl = "",
        [string]$InstallCommand = ""
    )

    Write-Host "`n🔍 檢查 $Name..." -ForegroundColor Yellow

    try {
        $version = & $Command --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $Name 已安裝: $version" -ForegroundColor Green
            if ($Verbose) {
                $path = (Get-Command $Command -ErrorAction SilentlyContinue).Source
                Write-Host "   路徑: $path" -ForegroundColor Gray
            }
            return $true
        } else {
            throw "Command failed with exit code $LASTEXITCODE"
        }
    } catch {
        Write-Host "❌ $Name 未安裝或不在 PATH 中" -ForegroundColor Red

        $issue = @{
            Type = "Missing"
            Component = $Name
            Command = $Command
            InstallUrl = $InstallUrl
            InstallCommand = $InstallCommand
            Description = "$Name 未安裝"
        }
        $script:issues += $issue

        if ($Fix -and $InstallCommand) {
            Write-Host "🔧 嘗試安裝 $Name..." -ForegroundColor Yellow
            try {
                Invoke-Expression $InstallCommand
                Write-Host "✅ $Name 安裝完成" -ForegroundColor Green
                return $true
            } catch {
                Write-Host "❌ $Name 自動安裝失敗: $($_.Exception.Message)" -ForegroundColor Red
            }
        }

        return $false
    }
}

function Check-Port {
    param(
        [int]$Port,
        [string]$Service
    )

    Write-Host "`n🔍 檢查埠號 $Port ($Service)..." -ForegroundColor Yellow

    try {
        $result = netstat -ano | findstr ":$Port"
        if ($result) {
            Write-Host "⚠️  埠號 $Port 被占用" -ForegroundColor Yellow
            if ($Verbose) {
                Write-Host "   占用詳情:" -ForegroundColor Gray
                $result | ForEach-Object { Write-Host "   $_" -ForegroundColor Gray }
            }

            $warning = @{
                Type = "PortInUse"
                Port = $Port
                Service = $Service
                Description = "埠號 $Port ($Service) 被占用"
            }
            $script:warnings += $warning

            if ($Fix) {
                Write-Host "🔧 嘗試釋放埠號 $Port..." -ForegroundColor Yellow
                try {
                    $processes = netstat -ano | findstr ":$Port"
                    foreach ($line in $processes) {
                        $parts = $line -split '\s+'
                        $pid = $parts[-1]
                        if ($pid -match '^\d+$') {
                            $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
                            if ($process) {
                                Write-Host "   終止程序: $($process.ProcessName) (PID: $pid)" -ForegroundColor Gray
                                Stop-Process -Id $pid -Force
                            }
                        }
                    }
                    Write-Host "✅ 埠號 $Port 已釋放" -ForegroundColor Green
                } catch {
                    Write-Host "❌ 無法釋放埠號 ${Port}: $($_.Exception.Message)" -ForegroundColor Red
                }
            }

            return $false
        } else {
            Write-Host "✅ 埠號 $Port 可用" -ForegroundColor Green
            return $true
        }
    } catch {
        Write-Host "⚠️  無法檢查埠號 ${Port}: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

function Check-Directory {
    param(
        [string]$Path,
        [string]$Description
    )

    Write-Host "`n🔍 檢查 $Description..." -ForegroundColor Yellow

    if (Test-Path $Path) {
        Write-Host "✅ $Description 存在: $Path" -ForegroundColor Green
        if ($Verbose) {
            $items = Get-ChildItem $Path | Measure-Object
            Write-Host "   包含 $($items.Count) 個項目" -ForegroundColor Gray
        }
        return $true
    } else {
        Write-Host "❌ $Description 不存在: $Path" -ForegroundColor Red

        $issue = @{
            Type = "MissingDirectory"
            Path = $Path
            Description = "$Description 不存在"
        }
        $script:issues += $issue

        return $false
    }
}

function Check-File {
    param(
        [string]$Path,
        [string]$Description
    )

    Write-Host "`n🔍 檢查 $Description..." -ForegroundColor Yellow

    if (Test-Path $Path -PathType Leaf) {
        Write-Host "✅ $Description 存在: $Path" -ForegroundColor Green
        if ($Verbose) {
            $size = (Get-Item $Path).Length
            Write-Host "   檔案大小: $size bytes" -ForegroundColor Gray
        }
        return $true
    } else {
        Write-Host "❌ $Description 不存在: $Path" -ForegroundColor Red

        $issue = @{
            Type = "MissingFile"
            Path = $Path
            Description = "$Description 不存在"
        }
        $script:issues += $issue

        return $false
    }
}

# 檢查基本工具
Write-Host "`n📋 檢查基本工具" -ForegroundColor Cyan

$pythonOk = Check-Command -Command "python" -Name "Python" -InstallUrl "https://python.org/downloads"
$nodeOk = Check-Command -Command "node" -Name "Node.js" -InstallUrl "https://nodejs.org"
$npmOk = Check-Command -Command "npm" -Name "npm"
$uvOk = Check-Command -Command "uv" -Name "uv" -InstallUrl "https://astral.sh/uv" -InstallCommand "irm https://astral.sh/uv/install.ps1 | iex"

# 檢查埠號
Write-Host "`n📋 檢查埠號可用性" -ForegroundColor Cyan

$mcpPortOk = Check-Port -Port 5000 -Service "MCP 服務器"
$proxyPortOk = Check-Port -Port 3000 -Service "Proxy 服務器"

# 檢查專案結構
Write-Host "`n📋 檢查專案結構" -ForegroundColor Cyan

$webEvalOk = Check-Directory -Path "web-eval-agent" -Description "web-eval-agent 目錄"
$proxyOk = Check-Directory -Path "prompt-proxy" -Description "prompt-proxy 目錄"
$frontendOk = Check-Directory -Path "frontend" -Description "frontend 目錄"

# 檢查關鍵檔案
Write-Host "`n📋 檢查關鍵檔案" -ForegroundColor Cyan

$mcpServerOk = Check-File -Path "web-eval-agent/webEvalAgent/mcp_server.py" -Description "MCP 服務器"
$multiLlmOk = Check-File -Path "web-eval-agent/webEvalAgent/src/multi_llm_handler.py" -Description "多 LLM 處理器"
$proxyServerOk = Check-File -Path "prompt-proxy/server.js" -Description "Proxy 服務器"
$forumHtmlOk = Check-File -Path "frontend/forum.html" -Description "前端頁面"
$stylesOk = Check-File -Path "frontend/styles.css" -Description "樣式檔案"
$appJsOk = Check-File -Path "frontend/app.js" -Description "JavaScript 檔案"

# 總結報告
Write-Host "`n" + "=" * 50
Write-Host "📊 環境檢查報告" -ForegroundColor Cyan

$totalIssues = $issues.Count
$totalWarnings = $warnings.Count

Write-Host ""
Write-Host "嚴重問題: $totalIssues" -ForegroundColor $(if ($totalIssues -eq 0) { "Green" } else { "Red" })
Write-Host "警告: $totalWarnings" -ForegroundColor $(if ($totalWarnings -eq 0) { "Green" } else { "Yellow" })

if ($totalIssues -eq 0 -and $totalWarnings -eq 0) {
    Write-Host "`n🎉 環境檢查通過！系統已準備就緒。" -ForegroundColor Green
    Write-Host "您可以執行 .\start-system.ps1 -Local 來啟動系統。" -ForegroundColor Green
} elseif ($totalIssues -eq 0) {
    Write-Host "`n⚠️  環境基本正常，但有一些警告需要注意。" -ForegroundColor Yellow
    Write-Host "系統應該可以正常運行，但建議處理警告項目。" -ForegroundColor Yellow
} else {
    Write-Host "`n❌ 發現嚴重問題，需要修復後才能正常運行。" -ForegroundColor Red
}

Write-Host "`n💡 建議操作:" -ForegroundColor Cyan

if ($totalIssues -gt 0) {
    Write-Host "  1. 修復上述嚴重問題" -ForegroundColor White
    if (-not $Fix) {
        Write-Host "  2. 或執行 .\check-setup.ps1 -Fix 嘗試自動修復" -ForegroundColor White
    }
} else {
    Write-Host "  1. 執行 .\start-system.ps1 -Local 啟動系統" -ForegroundColor White
    Write-Host "  2. 執行 .\test-system.ps1 進行功能測試" -ForegroundColor White
}

Write-Host "`n檢查完成！" -ForegroundColor Cyan

# 返回適當的退出碼
if ($totalIssues -eq 0) {
    exit 0
} else {
    exit 1
}
