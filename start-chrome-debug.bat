@echo off
echo Starting Chrome with Debug Port...
echo Please manually login to:
echo - ChatGPT: https://chat.openai.com
echo - Claude: https://claude.ai  
echo - Gemini: https://gemini.google.com
echo - Grok: https://grok.com
echo.
echo After login, run the main system.
echo.

start chrome --remote-debugging-port=9222 --user-data-dir=.\chrome-profile --no-first-run --no-default-browser-check

echo Chrome started with debug port 9222
echo Keep this window open while using the system
pause
