@echo off
chcp 65001 >nul
cd /d "%~dp0"

echo.
echo ========================================
echo   🚀 Multi-LLM 大亂鬥 啟動系統
echo ========================================
echo.

echo 📋 檢查系統需求...

:: 檢查 Python 和 uv
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到 Python，請先安裝 Python
    pause
    exit /b 1
)

uv --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到 uv，請先安裝 uv
    echo 安裝命令: curl -LsSf https://astral.sh/uv/install.sh ^| sh
    pause
    exit /b 1
)

echo ✅ Python 和 uv 已安裝

echo.
echo 🔧 啟動 MCP HTTP 橋接服務器...
echo 服務器地址: http://localhost:5000
echo.

:: 啟動 HTTP 橋接服務器
start "MCP HTTP Bridge Server" cmd /k "python mcp-http-bridge.py"

:: 等待服務器啟動
echo ⏳ 等待服務器啟動...
timeout /t 5 /nobreak >nul

echo.
echo 🌐 開啟前端介面...
start "" "forum.html"

echo.
echo ========================================
echo   ✅ 系統啟動完成！
echo ========================================
echo.
echo 📝 使用說明：
echo 1. 前端介面已在瀏覽器中開啟
echo 2. MCP 服務器運行在 http://localhost:5000
echo 3. 在前端輸入問題，選擇 AI 模型，點擊發送
echo 4. 系統會自動控制瀏覽器向各 AI 網站發送請求
echo.
echo 🔧 故障排除：
echo - 如果前端顯示連接錯誤，請檢查 MCP 服務器是否正常運行
echo - 確保已登入各 AI 網站（ChatGPT、Claude、Gemini、Grok）
echo - 如需重啟服務器，關閉命令視窗後重新運行此腳本
echo.
echo 按任意鍵關閉此視窗...
pause >nul
