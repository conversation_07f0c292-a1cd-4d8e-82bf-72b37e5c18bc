# Browser MCP Multi-LLM System Startup Script
param(
    [switch]$Local,
    [switch]$Cloud,
    [string]$ApiKey = ""
)

# Default to Local mode
if (-not $Local -and -not $Cloud) {
    $Local = $true
}

Write-Host "Browser MCP Multi-LLM System Starting..." -ForegroundColor Cyan
Write-Host "=" * 50

$mode = if ($Local) { "Local Mode" } else { "Cloud Mode" }
Write-Host "Mode: $mode" -ForegroundColor Yellow

# Check required tools
Write-Host "`nChecking required tools..." -ForegroundColor Yellow

$tools = @("python", "node", "npm", "uv")
foreach ($tool in $tools) {
    try {
        $null = & $tool --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "OK: $tool installed" -ForegroundColor Green
        } else {
            throw "Command failed"
        }
    } catch {
        Write-Host "ERROR: $tool not found" -ForegroundColor Red
        Write-Host "Please install required tools first" -ForegroundColor Yellow
        exit 1
    }
}

# Set environment variables
if ($Cloud -and $ApiKey) {
    $env:OPERATIVE_API_KEY = $ApiKey
    Write-Host "OK: API Key set" -ForegroundColor Green
}

# Install dependencies
Write-Host "`nInstalling dependencies..." -ForegroundColor Yellow

# Install Python dependencies
Write-Host "Installing Python dependencies..."
Set-Location "web-eval-agent"
try {
    uv pip install -e . | Out-Null
    Write-Host "OK: Python dependencies installed" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Python dependencies failed" -ForegroundColor Red
    exit 1
}

# Install Playwright
Write-Host "Installing Playwright browsers..."
try {
    uv run playwright install chromium | Out-Null
    Write-Host "OK: Playwright installed" -ForegroundColor Green
} catch {
    Write-Host "WARNING: Playwright install may have failed" -ForegroundColor Yellow
}

Set-Location ".."

# Install Node.js dependencies
Write-Host "Installing Node.js dependencies..."
Set-Location "prompt-proxy"
try {
    npm install | Out-Null
    Write-Host "OK: Node.js dependencies installed" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Node.js dependencies failed" -ForegroundColor Red
    exit 1
}
Set-Location ".."

# Start services
Write-Host "`nStarting services..." -ForegroundColor Yellow

# Start MCP server
Write-Host "Starting MCP server..."
Set-Location "web-eval-agent"

$mcpArgs = @("run", "python", "-m", "webEvalAgent.mcp_server", "--host", "0.0.0.0", "--port", "5000")
if ($Local) {
    $mcpArgs += "--local"
}

$mcpProcess = Start-Process -FilePath "uv" -ArgumentList $mcpArgs -NoNewWindow -PassThru
Write-Host "OK: MCP server starting (PID: $($mcpProcess.Id))" -ForegroundColor Green

Set-Location ".."

# Wait for MCP server
Write-Host "Waiting for MCP server..."
for ($i = 1; $i -le 30; $i++) {
    Start-Sleep -Seconds 1
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:5000/health" -TimeoutSec 2 -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            Write-Host "OK: MCP server ready" -ForegroundColor Green
            break
        }
    } catch {
        # Continue waiting
    }
    
    if ($i -eq 30) {
        Write-Host "ERROR: MCP server timeout" -ForegroundColor Red
        $mcpProcess.Kill()
        exit 1
    }
}

# Start Proxy server
Write-Host "Starting Proxy server..."
Set-Location "prompt-proxy"

$proxyProcess = Start-Process -FilePath "node" -ArgumentList "server.js" -NoNewWindow -PassThru
Write-Host "OK: Proxy server starting (PID: $($proxyProcess.Id))" -ForegroundColor Green

Set-Location ".."

# Wait for Proxy server
Write-Host "Waiting for Proxy server..."
for ($i = 1; $i -le 30; $i++) {
    Start-Sleep -Seconds 1
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000/health" -TimeoutSec 2 -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            Write-Host "OK: Proxy server ready" -ForegroundColor Green
            break
        }
    } catch {
        # Continue waiting
    }
    
    if ($i -eq 30) {
        Write-Host "ERROR: Proxy server timeout" -ForegroundColor Red
        $mcpProcess.Kill()
        $proxyProcess.Kill()
        exit 1
    }
}

# Show completion info
Write-Host "`n" + "=" * 50
Write-Host "System Started Successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Service Status:" -ForegroundColor Cyan
Write-Host "  - MCP Server: http://localhost:5000 (PID: $($mcpProcess.Id))" -ForegroundColor White
Write-Host "  - Proxy Server: http://localhost:3000 (PID: $($proxyProcess.Id))" -ForegroundColor White
Write-Host "  - Frontend: http://localhost:3000" -ForegroundColor White
Write-Host ""
Write-Host "Usage:" -ForegroundColor Cyan
Write-Host "  - Browser will open automatically" -ForegroundColor White
Write-Host "  - Select LLM models to use" -ForegroundColor White
Write-Host "  - Enter your prompt and send to multiple AIs" -ForegroundColor White
Write-Host "  - Press Ctrl+C to stop all services" -ForegroundColor White
Write-Host ""

# Auto open browser
try {
    Start-Process "http://localhost:3000"
    Write-Host "Browser opened automatically" -ForegroundColor Green
} catch {
    Write-Host "WARNING: Cannot open browser automatically" -ForegroundColor Yellow
    Write-Host "Please visit http://localhost:3000 manually" -ForegroundColor Yellow
}

# Wait for user interrupt
Write-Host "`nPress Ctrl+C to stop all services..." -ForegroundColor Gray

try {
    while ($true) {
        Start-Sleep -Seconds 1
    }
} finally {
    Write-Host "`nStopping services..." -ForegroundColor Yellow
    
    if ($mcpProcess -and -not $mcpProcess.HasExited) {
        $mcpProcess.Kill()
        Write-Host "OK: MCP server stopped" -ForegroundColor Green
    }
    
    if ($proxyProcess -and -not $proxyProcess.HasExited) {
        $proxyProcess.Kill()
        Write-Host "OK: Proxy server stopped" -ForegroundColor Green
    }
    
    Write-Host "All services stopped" -ForegroundColor Green
}
