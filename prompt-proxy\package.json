{"name": "browser-mcp-prompt-proxy", "version": "1.0.0", "description": "Prompt Proxy server for Browser MCP Multi-LLM system", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mcp", "browser", "llm", "proxy", "chatgpt", "claude", "gemini", "grok"], "author": "Browser MCP Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.2", "ws": "^8.14.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}