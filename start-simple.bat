@echo off
cd /d "%~dp0"

echo Starting Multi-LLM System...
echo.

echo Step 1: Starting MCP HTTP Bridge Server...
start "MCP Bridge" cmd /k "python mcp-http-bridge.py"

echo Step 2: Waiting for server startup...
timeout /t 3 /nobreak >nul

echo Step 3: Opening frontend interface...
start "" "forum.html"

echo.
echo System started successfully!
echo - MCP Server: http://localhost:5000
echo - Frontend: forum.html (opened in browser)
echo.
echo Press any key to exit...
pause >nul
