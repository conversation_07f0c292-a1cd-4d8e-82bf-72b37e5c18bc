@echo off
echo ========================================
echo Browser MCP Multi-LLM System Test
echo ========================================
echo.

echo 1. Opening Chrome Extensions page...
start "" "chrome://extensions/"
echo    Please load the browser-extension folder as an unpacked extension
echo.

echo 2. Waiting 5 seconds for extension setup...
timeout /t 5 /nobreak >nul
echo.

echo 3. Opening AI websites for login...
echo    Opening ChatGPT...
start "" "https://chat.openai.com"

echo    Opening Claude...
start "" "https://claude.ai"

echo    Opening Gemini...
start "" "https://gemini.google.com"

echo    Opening Grok...
start "" "https://grok.x.ai"

echo.
echo 4. Please log in to all AI websites before proceeding
echo.

echo 5. Waiting 10 seconds for login...
timeout /t 10 /nobreak >nul
echo.

echo 6. Opening Browser MCP Control Panel...
cd /d "%~dp0"
start "" "frontend\forum.html"

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Load the browser-extension in Chrome Extensions
echo 2. Log in to all AI websites
echo 3. Use the control panel to test multi-LLM chat
echo.
echo Press any key to exit...
pause >nul
