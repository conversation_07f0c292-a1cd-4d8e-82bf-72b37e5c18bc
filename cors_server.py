# 這是一個會發放「安全通行證」的 Python 伺服器
import http.server
import socketserver

PORT = 3000

class CORSRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        http.server.SimpleHTTPRequestHandler.end_headers(self)

Handler = CORSRequestHandler

with socketserver.TCPServer(("", PORT), Handler) as httpd:
    print("✅ 帶有安全通行證 (CORS) 的伺服器已啟動於 http://localhost:" + str(PORT))
    httpd.serve_forever()