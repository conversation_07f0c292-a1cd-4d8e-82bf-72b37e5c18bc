# Browser MCP Multi-LLM 安裝與使用指南

## 🚀 快速開始

### 第一步：安裝Chrome擴充功能

1. **開啟Chrome瀏覽器**
2. **前往擴充功能管理頁面**
   - 在網址列輸入：`chrome://extensions/`
   - 或點擊右上角三點選單 → 更多工具 → 擴充功能

3. **開啟開發人員模式**
   - 點擊右上角的「開發人員模式」開關

4. **載入擴充功能**
   - 點擊「載入未封裝項目」按鈕
   - 選擇專案中的 `browser-extension` 資料夾
   - 確認看到「Browser MCP Multi-LLM Controller」已載入

### 第二步：登入AI網站

**重要：使用前必須先登入以下AI網站**

1. **ChatGPT** - https://chat.openai.com
   - 需要OpenAI帳號
   
2. **Claude** - https://claude.ai  
   - 需要Anthropic帳號
   
3. **Gemini** - https://gemini.google.com
   - 需要Google帳號
   
4. **Grok** - https://grok.x.ai
   - 需要X (Twitter) 帳號

### 第三步：開啟控制面板

**方法一：透過擴充功能**
1. 點擊瀏覽器工具列中的Browser MCP圖標
2. 點擊「開啟控制面板」按鈕

**方法二：直接開啟**
1. 雙擊專案中的 `start.bat` 文件
2. 或直接開啟 `frontend/forum.html`

## 📋 使用步驟

### 1. 檢查狀態
- 開啟控制面板後，檢查右上角狀態指示器
- 「擴充功能」和「瀏覽器控制」都應該顯示為線上

### 2. 選擇AI模型
- 勾選想要使用的AI模型（ChatGPT、Claude、Gemini、Grok）
- 可以選擇一個或多個模型

### 3. 輸入問題
- 在大文字框中輸入您的問題或指令
- 支援多行文字和複雜問題

### 4. 發送請求
- 點擊「發送到所有選中的 LLM」按鈕
- 或使用快捷鍵 Ctrl+Enter

### 5. 查看回應
- 系統會自動開啟對應的AI網站分頁（在背景）
- 各AI的回應會即時顯示在控制面板中
- 支援串流顯示，可以看到回應逐字生成

## ⚡ 功能特色

- **一次輸入，多重回應**：同時獲得四個AI的不同觀點
- **即時串流顯示**：看到AI回應的生成過程
- **智能分頁管理**：自動管理AI網站，無需手動切換
- **統一格式呈現**：所有回應以一致格式顯示
- **匯出功能**：可匯出對話記錄
- **比較分析**：可開啟模態視窗比較不同AI的回應

## 🔧 故障排除

### 擴充功能狀態顯示離線
**解決方法：**
1. 確認已正確載入擴充功能
2. 重新整理控制面板頁面
3. 檢查Chrome擴充功能頁面是否有錯誤訊息

### AI網站無回應
**可能原因與解決方法：**
1. **未登入** - 確認已登入對應的AI網站
2. **網站異常** - 檢查AI網站是否正常運作
3. **分頁被阻擋** - 確認瀏覽器沒有阻擋彈出視窗
4. **網站更新** - AI網站UI更新可能影響功能

### 回應顯示異常
**解決步驟：**
1. 重新整理控制面板頁面
2. 重新載入擴充功能
3. 清除瀏覽器快取
4. 檢查瀏覽器開發者工具的Console錯誤

### 速度過慢或被限制
**注意事項：**
- 系統內建速率限制（每秒最多1個請求）
- 某些AI網站有使用頻率限制
- 建議適度使用，避免觸發反機器人機制

## 🛠️ 技術說明

### 系統架構
```
Browser MCP System
├── Chrome Extension (browser-extension/)
│   ├── Background Script - 核心控制邏輯
│   ├── Content Scripts - 各AI網站控制
│   └── Popup Interface - 狀態監控
└── Control Panel (frontend/)
    ├── forum.html - 主控制介面
    ├── app.js - 前端邏輯
    └── styles.css - 樣式設計
```

### 工作原理
1. **擴充功能注入**：Content scripts注入到各AI網站
2. **DOM操作**：自動填入問題並點擊發送
3. **回應監聽**：使用MutationObserver監聽AI回應
4. **即時傳輸**：透過Chrome API即時傳送回應到控制面板
5. **統一顯示**：在控制面板中統一格式顯示所有回應

## 📝 注意事項

- ⚠️ **登入要求**：使用前必須登入所有要使用的AI網站
- ⚠️ **使用限制**：遵守各AI平台的使用條款和頻率限制
- ⚠️ **網站更新**：AI網站UI更新可能影響功能
- ⚠️ **隱私安全**：所有對話都在您的瀏覽器中進行，不會傳送到第三方
- ⚠️ **實驗性質**：此工具為實驗性質，可能需要根據網站更新進行調整

## 🆘 需要幫助？

如果遇到問題：

1. **檢查Console**：按F12開啟開發者工具，查看Console錯誤訊息
2. **重新載入**：嘗試重新載入擴充功能和控制面板
3. **清除資料**：清除瀏覽器快取和Cookie
4. **更新瀏覽器**：確保使用最新版本的Chrome瀏覽器

## 🎯 使用建議

- **測試連接**：首次使用時建議先用簡單問題測試各AI的連接狀態
- **合理使用**：避免頻繁發送請求，給AI網站適當的處理時間
- **保存結果**：重要的對話記錄建議使用匯出功能保存
- **定期更新**：關注AI網站的更新，必要時更新擴充功能

---

**版本：** v1.0.0  
**更新日期：** 2024-12-29  
**相容性：** Chrome 88+ (Manifest V3)
