# Browser MCP 多 LLM 同步驅動系統 - 產品開發需求書 (PRD)

## 📋 文件資訊
- **版本**: v1.0
- **建立日期**: 2025-06-29
- **目標受眾**: 新進工程師
- **預期完成時間**: 30 分鐘內可複製執行

---

## 1. 產品願景 (Problem & Goal)

### 1.1 核心問題
開發者需要同時測試多個 LLM 的回應品質，但目前需要：
- 手動在 4 個分頁間切換
- 重複輸入相同的 prompt
- 無法並行比較回應結果

### 1.2 解決方案
使用 **Browser MCP + forum.html** 實現：
- 單次輸入 prompt，同步分發到 ChatGPT、Claude、Gemini、Grok 四個分頁
- 自動收集並展示所有回應
- 支援雲端沙盒與離線本機雙模式

### 1.3 價值主張
- **效率提升**: 4x 減少重複操作
- **並行比較**: 實時對比 LLM 回應品質
- **開發友好**: 支援離線開發，無需持續網路連線

---

## 2. 功能需求

### 2.1 核心功能

| 編號 | 功能 | 詳細說明 | 優先級 |
|------|------|----------|--------|
| F-1 | Prompt 同步分發 | forum.html → BrowserMCP → 4 LLM tabs，單次延遲 ≤ 3s | P0 |
| F-2 | 雙模式支援 | CLI 旗標 `--local` 切換離線；預設雲端模式需驗證 API Key | P0 |
| F-3 | HTTP 健康檢查 | `GET /health` 回傳 `{"status":"ok"}` | P1 |
| F-4 | 回應收集展示 | 自動收集 4 個 LLM 回應並在 forum.html 展示 | P0 |
| F-5 | 瀏覽器狀態管理 | 提供 `get_tabs`, `close_tab`, `send_prompt` API | P1 |
| F-6 | 流式回應支援 | WebSocket/SSE 實現回應流式顯示 | P2 |

### 2.2 工具 API

```python
# MCP Tools
@mcp.tool("web_eval_agent")
async def web_eval_agent(url: str, task: str, headless_browser: bool = False)

@mcp.tool("setup_browser_state") 
async def setup_browser_state(url: str = None)

@mcp.tool("send_prompt_to_llms")  # 新增
async def send_prompt_to_llms(prompt: str, models: list = ["chatgpt", "claude", "gemini", "grok"])
```

---

## 3. 非功能需求

| 類別 | 指標 | 說明 |
|------|------|------|
| **安裝時間** | < 30 分鐘 | 新人完整走完五步安裝流程 |
| **跨平台支援** | Windows 10+, macOS, Linux | PowerShell 5.1+, Bash, Zsh |
| **資安要求** | API Key 環境變數 | 不落檔，僅讀取環境變數 |
| **可維護性** | 版本鎖定 | uv.lock 固定所有依賴版本 |
| **效能要求** | 並發處理 | 支援 4 個瀏覽器分頁同時操作 |

---

## 4. 技術架構

### 4.1 系統組件

```mermaid
graph TD
    A[forum.html] --> B[Prompt Proxy Server:3000]
    B --> C[Browser MCP Server:5000]
    C --> D[ChatGPT Tab]
    C --> E[Claude Tab]
    C --> F[Gemini Tab]
    C --> G[Grok Tab]
    D --> H[Response Collector]
    E --> H
    F --> H
    G --> H
    H --> A
```

### 4.2 核心技術棧

- **後端**: FastMCP + Python 3.11+
- **瀏覽器自動化**: Playwright + browser-use
- **前端**: Vanilla JS + Fetch API + SSE
- **包管理**: uv (Python)
- **傳輸協議**: HTTP (開發) / stdio (生產)

---

## 5. 安裝流程

### 5.1 環境準備
```powershell
# 檢查 Python 版本
python --version  # 需要 3.11+

# 安裝 uv
irm https://astral.sh/uv/install.ps1 | iex
```

### 5.2 五步安裝流程

```powershell
# 步驟 1: 進入專案目錄
cd "C:\Users\<USER>\path\to\web-eval-agent"

# 步驟 2: 建立虛擬環境
uv venv .venv

# 步驟 3: 安裝專案依賴
uv pip install -e .

# 步驟 4: 安裝瀏覽器
uv run playwright install chromium

# 步驟 5: 啟動服務
# 雲端模式 (需 API Key)
$Env:OPERATIVE_API_KEY="op-xxxxxxxxxxxxx"
uv run python -m webEvalAgent.mcp_server --host 0.0.0.0 --port 5000

# 或離線模式 (無需 API Key)
uv run python -m webEvalAgent.mcp_server --local --host 0.0.0.0 --port 5000
```

### 5.3 健康檢查
```powershell
# 檢查服務狀態
curl http://localhost:5000/health
# 預期輸出: {"status":"ok"}
```

---

## 6. 配置參數

| 參數 | 說明 | 預設值 | 範例 |
|------|------|--------|------|
| `--transport` | MCP 傳輸協議 | stdio | http, sse |
| `--local` | 離線模式開關 | False | --local |
| `--host` | 綁定地址 | 127.0.0.1 | 0.0.0.0 |
| `--port` | 監聽埠號 | 5000 | 5001 |
| `--headless` | 無頭瀏覽器 | False | --headless |

---

## 7. 常見問題與解決方案

### 7.1 安裝階段

| 問題 | 症狀 | 解決方案 |
|------|------|----------|
| uv 命令未找到 | `uv: command not found` | 重新開啟終端或重新執行安裝腳本 |
| 模組未找到 | `ModuleNotFoundError: webEvalAgent` | 確認在含 pyproject.toml 的目錄執行安裝 |
| 權限問題 | OneDrive 鎖檔 | 移動專案至 `C:\Projects` 或使用管理員權限 |

### 7.2 運行階段

| 問題 | 症狀 | 解決方案 |
|------|------|----------|
| 埠號衝突 | `EADDRINUSE :5000` | `netstat -ano \| findstr :5000` 後 `taskkill /PID <ID> /F` |
| API Key 無效 | `Invalid API key` | 檢查環境變數設定或使用 `--local` 模式 |
| 瀏覽器啟動失敗 | Playwright 錯誤 | 執行 `uv run playwright install chromium` |

### 7.3 PowerShell 特殊問題

```powershell
# PowerShell 5.1 不支援 && 操作符
# 錯誤寫法
cd web-eval-agent && uv run python -m webEvalAgent.mcp_server

# 正確寫法
cd web-eval-agent
uv run python -m webEvalAgent.mcp_server
```

---

## 8. 開發里程碑

| 階段 | 時間 | 交付物 | 驗收標準 |
|------|------|--------|----------|
| Phase 1 | T+0d | PRD 定稿 | 本文件完成 |
| Phase 2 | T+3d | 基礎 MCP 服務 | 健康檢查通過 |
| Phase 3 | T+7d | 多瀏覽器控制 | 4 個分頁同時操作 |
| Phase 4 | T+14d | 前端整合 | forum.html 完整流程 |

---

## 9. 驗收標準

### 9.1 功能驗收
1. **健康檢查**: `curl /health` 回傳 200 狀態碼
2. **多 LLM 同步**: forum.html 輸入 prompt，4 個模型卡片均顯示回應 ≤ 10s
3. **重裝測試**: 刪除 `.venv` 後，按 README 重裝並通過上述測試

### 9.2 效能驗收
- 單次 prompt 分發延遲 < 3s
- 4 個瀏覽器分頁並發處理無衝突
- 記憶體使用 < 2GB (包含 4 個瀏覽器實例)

### 9.3 穩定性驗收
- 連續運行 1 小時無崩潰
- 處理 100 次 prompt 請求無記憶體洩漏
- 網路中斷後自動重連

---

## 10. 後續擴展

### 10.1 短期優化 (1-2 週)
- 支援更多 LLM 模型 (Perplexity, Cohere)
- 回應結果匯出功能 (JSON, CSV)
- 簡單的回應品質評分

### 10.2 中期功能 (1-2 月)
- Web UI 管理介面
- 批量 prompt 測試
- A/B 測試框架

### 10.3 長期願景 (3-6 月)
- 雲端部署版本
- 團隊協作功能
- 自動化測試報告

---

## 11. 實現細節

### 11.1 關鍵代碼修復

**Windows netstat 命令修復** (已解決):
```python
# webEvalAgent/src/utils.py - 修復前
subprocess.run(["netstat", "-ano", "|", "findstr", ":5009"])  # ❌ 錯誤

# 修復後
result = subprocess.run(["netstat", "-ano"], capture_output=True, text=True)
for line in result.stdout.split('\n'):
    if ':5009' in line and 'LISTENING' in line:
        pid = line.split()[-1]
        subprocess.run(["taskkill", "/F", "/PID", pid])  # ✅ 正確
```

### 11.2 forum.html 核心邏輯

```javascript
// 同步發送 prompt 到 4 個 LLM
async function sendToAllLLMs(prompt) {
    const models = ['chatgpt', 'claude', 'gemini', 'grok'];
    const promises = models.map(model =>
        fetch('/api/send-prompt', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ model, prompt })
        })
    );

    const responses = await Promise.all(promises);
    return responses.map(r => r.json());
}
```

### 11.3 MCP 服務器配置

```json
// IDE MCP 配置 (Cursor/Windsurf)
{
  "mcpServers": {
    "web-eval-agent": {
      "command": "uv",
      "args": ["run", "python", "-m", "webEvalAgent.mcp_server", "--local"],
      "cwd": "web-eval-agent",
      "env": {
        "OPERATIVE_API_KEY": "op-your-key-here"
      }
    }
  }
}
```

### 11.4 快速診斷腳本

```powershell
# check-setup.ps1 - 環境檢查腳本
Write-Host "🔍 檢查 Python 版本..."
python --version

Write-Host "🔍 檢查 uv 安裝..."
uv --version

Write-Host "🔍 檢查專案結構..."
if (Test-Path "pyproject.toml") {
    Write-Host "✅ pyproject.toml 存在"
} else {
    Write-Host "❌ 請確認在正確目錄"
}

Write-Host "🔍 檢查埠號 5000..."
$port5000 = netstat -ano | findstr :5000
if ($port5000) {
    Write-Host "⚠️  埠號 5000 被占用: $port5000"
} else {
    Write-Host "✅ 埠號 5000 可用"
}
```

## 12. 附錄

### 12.1 相關連結
- [web-eval-agent GitHub](https://github.com/Operative-Sh/web-eval-agent)
- [FastMCP 文件](https://github.com/jlowin/fastmcp)
- [Playwright 文件](https://playwright.dev/docs/browsers)
- [uv 包管理器](https://docs.astral.sh/uv/)

### 12.2 聯絡資訊
- **技術負責人**: [您的姓名]
- **專案 Slack**: #browser-mcp-project
- **問題回報**: GitHub Issues

### 12.3 版本歷史
- **v1.0** (2025-06-29): 初版 PRD，包含完整安裝流程
- **v0.9** (2025-06-15): 解決 Windows PowerShell 兼容性問題
- **v0.8** (2025-06-01): 基礎 MCP 服務器實現

---

*本 PRD 基於實際開發經驗編寫，涵蓋了 Windows 環境下的常見踩雷點。按此需求書執行，可確保「forum.html → Prompt Proxy → Browser MCP → 4 LLM」流程穩定運轉。*
